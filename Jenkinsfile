node {
    stage('pull-code') {
        checkout scm
    }

    try {
            stage('deploy-mshop-cms-manager') {
                sh "/usr/local/maven/bin/mvn -U -DskipTests -f ${env.WORKSPACE}/pom.xml clean deploy"
            }
        } catch (e){
            echo 'deploy-mshop-cms-manager deploy failed'
        }

    try {
             stage('deploy->mshop-cms-manager-service-facade-common') {
                  sh "/usr/local/maven/bin/mvn -U -DskipTests -f ${env.WORKSPACE}/mshop-cms-manager-service-facade-common/pom.xml clean deploy"
             }
         } catch (e){
            echo 'mshop-cms-manager-service-facade-common deploy failed'
         }
}