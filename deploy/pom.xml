<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mengxiang.mshop.cms</groupId>
        <artifactId>mshop-cms-manager</artifactId>
        <version>1.0.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mshop-cms-manager-deploy</artifactId>
    <packaging>war</packaging>

    <name>mshop-cms-manager-deploy</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mengxiang.mshop.cms</groupId>
            <artifactId>mshop-cms-manager-web-manager</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.mshop.cms</groupId>
            <artifactId>mshop-cms-manager-biz-service-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>base-metrics-client</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>mshop-cms-manager</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.mengxiang.mshop.cms.Application</mainClass>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
