package com.mengxiang.mshop.cms;

import com.mengxiang.base.common.rpc.support.annotation.MXEnableCloud;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;

/**
 * 应用启动入口
 * @version $
 * **包扫描路径尽可能小**
 * <AUTHOR>
@SpringBootApplication(
        exclude = {
                DataSourceAutoConfiguration.class,
        }
)
@MXEnableCloud(basePackages = {"com.aikucun.security.sso.permission.feign",
        "com.akucun.sp.stub.api",
        "com.aikucun.dc.aiward.facade.stub",
        "com.akucun.mshop.gateway",
        "com.akucun.base.shorturl.stub",
        "com.akucun.meroperationtool.facade",
        "com.x.live.center.remote"})
@ComponentScan(basePackages = {"com.mengxiang.mshop.cms",
        "com.aikucun.security.sso",
        "com.akucun.sp.stub.api",
        "com.aikucun.dc.aiward.facade.stub",
        "com.akucun.mshop.gateway",
        "com.akucun.base.shorturl.stub",
        "com.akucun.meroperationtool.facade",
        "com.x.live.center.remote"})
public class Application extends SpringBootServletInitializer {
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(Application.class);
    }

    public static void main(String[] args) {
        new SpringApplication(Application.class).run(args);
    }
}