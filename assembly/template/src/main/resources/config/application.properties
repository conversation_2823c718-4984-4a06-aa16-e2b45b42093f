spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.url=************************************************************************************
spring.datasource.username=xxx
spring.datasource.password=xxx
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.data-source-properties.cachePrepStmts=true
spring.datasource.hikari.data-source-properties.leakDetectionThreshold=4000
spring.datasource.hikari.data-source-properties.prepStmtCacheSize=250
spring.datasource.hikari.data-source-properties.prepStmtCacheSqlLimit=2048
spring.datasource.hikari.data-source-properties.useServerPrepStmts=true
spring.datasource.hikari.maximumPoolSize=40
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.pool-name=mshop-cms-manager-hikariCP
spring.redis.database=0
spring.redis.host=127.0.0.1
spring.redis.password=
spring.redis.port=6379
spring.redis.timeout=3s
spring.redis.pool.max-active=20
spring.redis.pool.max-idle=8
spring.redis.pool.max-wait=3000
spring.redis.pool.min-idle=0
eureka.instance.instance-id=${spring.cloud.client.ip-address}:${server.port}
eureka.instance.prefer-ip-address=true
eureka.instance.status-page-url-path=swagger-ui.html
eureka.client.service-url.defaultZone=${eureka}

#httpClient å³é­
feign.httpclient.enabled = false 
#okHttpClient å¯ç¨
feign.okhttp.enabled = true 
#æå¤§è¿æ¥æ°
feign.httpclient.maxConnections = 1000   
#feignåä¸ªè·¯å¾çæå¤§è¿æ¥æ°
feign.httpclient.maxConnectionsPerRoute = 300   
#è¶æ¶æ¶é´
feign.httpclient.connectionTimeout = 3000  