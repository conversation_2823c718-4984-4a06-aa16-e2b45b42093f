# 基于PageController的标准会场系统技术方案

## 1. 系统架构概述

### 1.1 整体架构
基于Spring Boot + Spring Cloud微服务架构，采用分层设计模式：
- **Web层**: Controller层，提供RESTful API接口
- **Service层**: 业务逻辑处理层  
- **Integration层**: 外部服务集成层
- **Model层**: 数据模型和枚举定义层

### 1.2 核心技术栈
- **框架**: Spring Boot 2.x + Spring Cloud
- **API文档**: Swagger/OpenAPI 3.0
- **数据访问**: MyBatis + MySQL
- **缓存**: Redis
- **配置中心**: Apollo
- **服务治理**: Feign + Hystrix

## 2. 核心业务功能

### 2.1 页面生命周期管理

#### 页面状态枚举
```java
// 页面状态：1.待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布（审批通过）5.页面失效
public enum PageStatus {
    DRAFT(1, "待发布"),
    APPROVING(2, "审批中"), 
    REJECTED(3, "审批驳回"),
    PUBLISHED(4, "已发布"),
    INVALID(5, "页面失效")
}
```

#### 页面类型分类
```java
public enum PageType {
    HOME_PAGE("HOME", "首页"),
    SHOP_PAGE("SHOP", "店铺主页"),
    MARKET_PAGE("MARKET", "会场"),
    CATEGORY_PAGE("CATEGORY", "分类页"),
    SHOP_MICRO_PAGE("SHOPMICRO", "微页面")
}
```

### 2.2 核心API接口设计

#### 页面管理接口
```java
@RestController
@RequestMapping("/api/page")
@Api(value = "商户云-页面搭建服务")
public class PageController {
    
    // 页面详情查询
    @GetMapping("/manager/detail")
    public Result<PageBO> detail(@RequestParam String pageCode, @RequestParam String version);
    
    // 页面列表分页查询
    @PostMapping("/manager/pageSelect")
    public Result<Pagination<PageSelectResp>> pageSelect(@RequestBody PageSearchReq req);
    
    // 页面保存/发布
    @PostMapping("/save")
    public Result<PageBO> save(@RequestBody SavePageRequest req);
    
    // 页面失效
    @GetMapping("/manager/setPageInvalidation")
    public Result<Boolean> setPageInvalidation(@RequestParam String pageCode, @RequestParam String version);
    
    // 页面预览
    @GetMapping("/preview")
    public Result<Map<String, PageMengXiangPrewLink>> preview(@RequestParam String pageCode, @RequestParam String version);
    
    // 生成页面链接
    @GetMapping("/generateUrl")
    public Result<Map<String,String>> generatePageUrl(@RequestParam String pageCode);
}
```

## 3. 数据模型设计

### 3.1 核心数据模型

#### 页面基础信息模型
```java
public class PageBO {
    private String pageCode;        // 页面编号
    private String name;            // 页面名称
    private String title;           // 页面标题
    private String subTitle;        // 页面副标题
    private String version;         // 版本号
    private Integer status;         // 页面状态
    private String type;            // 页面类型
    private String ownerId;         // 所属者ID
    private String ownerType;       // 所属者类型
    private String tenantId;        // 租户ID
    private String channel;         // 使用渠道
    private String components;      // 页面组件配置JSON
    private String templateCode;    // 模板编号
    private TimeConfigBO timeConfig; // 时间配置
    private String createBy;        // 创建人
    private String updateBy;        // 修改人
    private Date createTime;        // 创建时间
    private Date updateTime;        // 更新时间
}
```

#### 页面查询请求模型
```java
public class PageSearchReq extends PagingRequest {
    private List<String> pageCodeList;  // 页面编号集合
    private String name;                // 页面名称
    private String title;               // 页面标题
    private String tenantId;            // 租户ID
    private String ownerType;           // 所属者类型
    private String ownerId;             // 所属者ID
    private Integer status;             // 页面状态
    private String type;                // 页面类型
    private List<String> typeList;      // 页面类型列表
    private Date startTime;             // 开始时间
    private Date endTime;               // 结束时间
}
```

### 3.2 多租户支持
```java
public enum PageOwnerType {
    SUPPLIER("supplier", "商家"),
    MENGXIANG("mengxiang", "梦饷平台"),
    SAAS_TENANT("tenant", "SaaS租户"),
    SYSTEM("system", "系统")
}
```

### 3.3 多渠道支持
```java
public enum TemplateUseChannel {
    APP("app", "APP端"),
    H5("h5", "H5端"),
    MINIAPP("miniapp", "小程序端")
}
```

## 4. 业务流程设计

### 4.1 会场创建流程
1. **用户身份验证**: 验证商户用户登录状态
2. **权限检查**: 检查用户是否有创建会场的权限
3. **模板选择**: 用户选择会场模板或使用默认模板
4. **页面配置**: 配置页面基础信息（名称、标题、时间等）
5. **组件配置**: 配置页面组件内容
6. **保存草稿**: 保存为草稿状态，可继续编辑
7. **提交审批**: 提交发布申请，进入审批流程
8. **审批处理**: 审批通过后页面正式发布

### 4.2 会场编辑流程
```java
// 页面保存核心逻辑
public Result<PageBO> save(SavePageRequest req, MerchantUserInfo userInfo) {
    // 1. 用户身份验证
    if(userInfo == null) {
        return Result.error("用户未登录");
    }
    
    // 2. 设置页面基础信息
    req.setTenantId(tenantId);
    req.setChannel(TemplateUseChannel.APP.getChannel());
    req.setOwnerId(userInfo.getShopCode());
    req.setOwnerType(PageOwnerType.SUPPLIER.getOwnerType());
    req.setCreateBy(userInfo.getUsername());
    req.setUpdateBy(userInfo.getUsername());
    
    // 3. 调用业务服务保存
    return pageService.saveByMer(req, userInfo);
}
```

### 4.3 会场预览流程
```java
// 页面预览逻辑
public Result<Map<String, PageMengXiangPrewLink>> preview(String pageCode, String version, Integer type) {
    // 参数验证
    if(StringUtils.isBlank(pageCode) || StringUtils.isBlank(version)) {
        return Result.error("参数不能为空");
    }
    
    // 根据类型选择预览方式
    if(Objects.equals(type, 2)) {
        // 商家会场3.0预览
        return Result.success(mengXiangService.preview(null, pageCode, version));
    } else {
        // 商家店铺装修预览
        return Result.success(pageService.previewShop(null, pageCode, version, shopCode));
    }
}
```

## 5. 技术实现方案

### 5.1 分层架构实现

#### Controller层职责
- 接收HTTP请求，参数验证
- 用户身份验证和权限检查
- 调用Service层业务逻辑
- 返回统一格式的响应结果

#### Service层职责
```java
public interface PageService {
    // 页面CRUD操作
    Result<PageBO> save(SavePageRequest request);
    Result<PageBO> detail(String pageCode, String version);
    Result<Pagination<PageSelectResp>> pageSelect(PageSearchReq req);
    Result<Boolean> setPageInvalidation(String pageCode, String version);
    
    // 页面业务操作
    Result<PageBO> detailByTemplate(String templateCode);
    Result<PageBO> detailToBeforePublished(String pageCode);
    Result<PageBO> detailToNewPage(String pageCode, String version);
    Map<String, PageMengXiangPrewLink> previewShop(String staffId, String pageCode, String version, String shopCode);
    
    // 页面管理操作
    Result<PageBO> createDefaultPage(String shopCode);
    Result<Void> setPageIndex(String pageCode, String shopCode, String pageType, String ownerType);
}
```

### 5.2 权限控制机制
```java
// 用户身份验证
@ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo

// 权限检查示例
if(null == userInfo) {
    return Result.error("用户未登录");
}

// 数据权限控制
req.setOwnerId(userInfo.getShopCode());
req.setOwnerType(PageOwnerType.SUPPLIER.getOwnerType());
```

### 5.3 审批流程机制
```java
// 会场页面需要审批
if (savePageRequest.getType().equals(PageType.MARKET_PAGE.getType())) {
    // 查询审批人列表
    buildAuditList(savePageRequest, userInfo.getShopCode());
}
```

## 6. 安全与权限控制

### 6.1 用户身份验证
- 基于Session的用户登录验证
- 支持商户用户、管理员用户、租户用户等多种用户类型
- 统一的用户信息获取机制

### 6.2 数据隔离策略
- **租户级隔离**: 基于tenantId实现租户数据隔离
- **商家级隔离**: 基于ownerId和ownerType实现商家数据隔离
- **页面级权限**: 用户只能操作自己拥有的页面

### 6.3 操作日志记录
```java
// 操作日志记录
@GetMapping("/operationLog/addByEDIT")
public Result<Boolean> operationLogAddByEDIT(String pageCode) {
    OperationLogSaveReq request = new OperationLogSaveReq();
    request.setAction(OperationLogActionEnum.EDIT.getCode());
    request.setBizCode(pageCode);
    request.setBizType(1);
    request.setRemark(OperationLogActionEnum.EDIT.getDesc());
    return operationLogService.saveOperationLog(request);
}
```

## 7. 性能优化方案

### 7.1 分页查询优化
- 支持多条件组合查询
- 数据库索引优化
- 分页参数合理性校验

### 7.2 缓存策略
- 页面模板缓存
- 用户信息缓存
- 配置信息缓存

### 7.3 异步处理
- 页面发布异步处理
- 预览链接异步生成
- 审批流程异步通知

## 8. 扩展性设计

### 8.1 多业务场景支持
- 商户云页面搭建
- 梦饷平台页面管理
- SaaS租户页面服务

### 8.2 多端适配
- APP端页面
- H5端页面
- 小程序端页面

### 8.3 模板扩展
- 支持自定义页面模板
- 模板版本管理
- 组件化设计

## 9. 部署与运维

### 9.1 配置管理
```yaml
# Apollo配置示例
page:
  tenantId: 151738493257170900
  merchant:
    default:
      create:
        components: ""
        templateCode: "2"
```

### 9.2 监控告警
- 接口响应时间监控
- 业务成功率监控
- 系统资源监控

### 9.3 日志管理
- 结构化日志输出
- 日志级别控制
- 日志归档策略

## 10. 总结

本技术方案基于现有PageController架构，提供了完整的会场管理功能：

**核心特性**:
- 多租户支持
- 多渠道适配
- 完整的页面生命周期管理
- 灵活的权限控制
- 可扩展的模板系统

**技术优势**:
- 分层架构清晰
- 接口设计规范
- 安全机制完善
- 性能优化到位
- 扩展性良好

该方案可以满足标准会场系统的各种业务需求，具有良好的可维护性和扩展性。
