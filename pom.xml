<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mengxiang.base</groupId>
        <artifactId>base-parent</artifactId>
        <version>1.1.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.mengxiang.mshop.cms</groupId>
    <artifactId>mshop-cms-manager</artifactId>
    <packaging>pom</packaging>
    <version>1.0.2</version>

    <modules>
        <module>mshop-cms-manager/common/dal</module>
        <module>mshop-cms-manager/common/util</module>
        <module>mshop-cms-manager/common/service/integration</module>
        <module>mshop-cms-manager/core/model</module>
        <module>mshop-cms-manager/core/service</module>
        <module>mshop-cms-manager/service/facade/common</module>
        <module>mshop-cms-manager/biz/shared</module>
        <module>mshop-cms-manager/biz/manager</module>
        <module>mshop-cms-manager/biz/service/impl</module>
        <module>mshop-cms-manager/test</module>
        <module>mshop-cms-manager/web/manager</module>
        <module>assembly/template</module>
        <module>deploy</module>
    </modules>
    
    <!--版本控制-->
    <properties>
        <merchant-platform-facade-stub.version>2.2.62</merchant-platform-facade-stub.version>
        <merchant-platform-query-service-facade-token-starter.version>2.0.4</merchant-platform-query-service-facade-token-starter.version>
        <mshop-cms-center-service-facade-common.version>1.2.0</mshop-cms-center-service-facade-common.version>
        <sso-authentication.version>0.0.7</sso-authentication.version>
        <sso-permission.version>0.0.9.4</sso-permission.version>
    </properties>
    
    
    <dependencyManagement>
        <dependencies>
            <!-- demo project depends -->
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-common-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-common-util</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-common-service-integration</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-core-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-core-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-service-facade-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-biz-shared</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-biz-manager</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-biz-service-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-assembly-template</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-deploy</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-test</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-manager-web-manager</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mengxiang.merchant.platform.query</groupId>
                <artifactId>merchant-platform-query-service-facade-common</artifactId>
                <version>2.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.akucun.merchant</groupId>
                <artifactId>merchant-platform-facade-stub</artifactId>
                <version>${merchant-platform-facade-stub.version}</version>
            </dependency>
            <dependency>
                <groupId>com.akucun</groupId>
                <artifactId>akucun-meroperationtool-management-facade-stub</artifactId>
                <version>2.0.4</version>
            </dependency>
    
            <dependency>
                <groupId>com.mengxiang.mshop.cms</groupId>
                <artifactId>mshop-cms-center-service-facade-common</artifactId>
                <version>${mshop-cms-center-service-facade-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mengxiang.mshop.aggr</groupId>
                <artifactId>mshop-aggr-prod-service-facade-common</artifactId>
                <version>1.0.12</version>
            </dependency>
    
            <dependency>
                <groupId>com.mengxiang.merchant.platform.query</groupId>
                <artifactId>merchant-platform-query-service-facade-token-starter</artifactId>
                <version>${merchant-platform-query-service-facade-token-starter.version}</version>
            </dependency>


            <dependency>
                <groupId>com.aikucun.security.sso</groupId>
                <artifactId>security-sso-authentication</artifactId>
                <version>${sso-authentication.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aikucun.security.sso</groupId>
                <artifactId>security-sso-permission</artifactId>
                <version>${sso-permission.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.promo</groupId>
                <artifactId>promo-service-facade</artifactId>
                <version>2.3.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>jakarta.validation</groupId>
                        <artifactId>jakarta.validation-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.mengxiang.base</groupId>
                        <artifactId>common-rpc-annotation</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.mengxiang.commodity.price.query</groupId>
                        <artifactId>com-price-query-service-facade-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.akucun.sp</groupId>
                        <artifactId>akucun-sp-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.akucun.sp</groupId>
                <artifactId>akucun-sp-model</artifactId>
                <version>2.4.2.5</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.akucun.sp</groupId>
                        <artifactId>akucun-sp-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun.sp</groupId>
                <artifactId>akucun-sp-stub</artifactId>
                <version>2.4.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.akucun.sp</groupId>
                        <artifactId>akucun-sp-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>base-metrics-client</artifactId>
                <version>2.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.akucun.member.auth</groupId>
                <artifactId>member-auth-spring-starter</artifactId>
                <version>1.2.4</version>
            </dependency>
            <dependency>
                <groupId>com.akucun.mshop.gateway</groupId>
                <artifactId>wx-gateway-stub</artifactId>
                <version>0.2.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>retrofit-spring-boot-starter</artifactId>
                        <groupId>com.github.lianjiatech</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aikucun.dc</groupId>
                <artifactId>aikucun-dc-aiward-facade-stub</artifactId>
                <version>1.1.12</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>2.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.saas</groupId>
                <artifactId>tenant-core-service-facade-common</artifactId>
                <version>1.2.63</version>
            </dependency>
            <dependency>
                <groupId>com.akucun.base</groupId>
                <artifactId>base-short-url-stub</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83_noneautotype</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <defaultGoal>install</defaultGoal>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>http://maven.aikucun.com:8082/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>nexus-releases</id>
            <url>http://maven.aikucun.com:8082/nexus/content/repositories/releases/</url>
        </repository>
    </distributionManagement>
</project>
