<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mengxiang.mshop.cms</groupId>
        <artifactId>mshop-cms-manager</artifactId>
        <version>1.0.2</version>
        <relativePath>../../../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mshop-cms-manager-service-facade-common</artifactId>
    <packaging>jar</packaging>

    <name>mshop-cms-manager-service-facade-common</name>

    <dependencies>
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>common-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>common-rpc-annotation</artifactId>
        </dependency>

        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <!-- Getter，Setter，toString -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.aikucun.common2</groupId>
            <artifactId>common2-base</artifactId>
            <version>1.2.9</version>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.merchant.platform.query</groupId>
            <artifactId>merchant-platform-query-service-facade-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.akucun.merchant</groupId>
            <artifactId>merchant-platform-facade-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>akucun-meroperationtool-management-facade-stub</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.mshop.aggr</groupId>
            <artifactId>mshop-aggr-prod-service-facade-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.promo</groupId>
            <artifactId>promo-service-facade</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
