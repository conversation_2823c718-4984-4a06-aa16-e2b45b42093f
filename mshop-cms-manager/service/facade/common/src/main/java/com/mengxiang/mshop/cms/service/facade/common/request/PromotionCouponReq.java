package com.mengxiang.mshop.cms.service.facade.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PromotionCouponReq {

    @ApiModelProperty(value = "营销活动版本 必传 NEW= 新版本 OLD= 老版本",required = true)
    private String promoActivityVersion;

    @ApiModelProperty(value = "营销活动所属方 PLATFORM 平台券 MERCHANT 商家券",required = true)
    private String promoActivityOwner;

    @ApiModelProperty(value = "营销活动ID",required = true)
    private String promoActivityId;
    @ApiModelProperty("是否是查询积分兑换优惠券,默认false")
    private Boolean pointsCoupon = Boolean.FALSE;
}