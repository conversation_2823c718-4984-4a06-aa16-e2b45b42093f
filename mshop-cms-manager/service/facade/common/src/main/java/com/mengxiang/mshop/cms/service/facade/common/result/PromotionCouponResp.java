package com.mengxiang.mshop.cms.service.facade.common.result;

import com.mengxiang.promo.service.facade.response.PromotionCouponInfoResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PromotionCouponResp extends PromotionCouponInfoResp {

    @ApiModelProperty("活动范围 ACTIVITY：档期活动，商品SKU：SKU")
    private String promoActivityRange;

    @ApiModelProperty("券面额")
    private String coupon;

    @ApiModelProperty("优惠券开始时间、结束时间:2023.02.20 10:00-2023.02.25 10:00")
    private String timeDesc;

    @ApiModelProperty("营销活动名称")
    private String promoActivityName;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty(value = "营销活动ID")
    private String promoActivityId;

    @ApiModelProperty(value = "商家名称")
    private String merName;
}
