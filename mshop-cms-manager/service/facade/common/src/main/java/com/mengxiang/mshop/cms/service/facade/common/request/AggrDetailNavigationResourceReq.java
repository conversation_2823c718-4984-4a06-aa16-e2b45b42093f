package com.mengxiang.mshop.cms.service.facade.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class AggrDetailNavigationResourceReq {
    @ApiModelProperty("主键id")
    private Integer id;
    @ApiModelProperty("商家用户id")
    private String tenantUserId;
    @ApiModelProperty("操作类型(0:类目tab 1.类目bannner,2:活动/商品banner,3.金刚位,4.小胶囊位,5:胶囊位,6.插卡位,7:连体胶囊位,8:找货/素材/学堂banner(后续20开始))")
    private Integer optType;
    @ApiModelProperty("是否删除(0:显示 1:删除)")
    private Integer isDelete;
    @ApiModelProperty("创建人")
    private String creater;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改人")
    private String updater;
    @ApiModelProperty("修改时间")
    private Date updateTime;
    @ApiModelProperty("是否展示集团日（0:不展示 1:展示）")
    private Integer blocDayShowFlag;
    @ApiModelProperty("类目开关code")
    private String categoryId;
    @ApiModelProperty("开关状态:ON/OFF")
    private String resourceSwitch;
    @ApiModelProperty("排序字段")
    private Integer sortValue;
    @ApiModelProperty(value = "导航类型 1=自定义导航 2=平台页面导航")
    private String sourceType;
}
