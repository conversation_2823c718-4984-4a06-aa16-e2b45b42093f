package com.mengxiang.mshop.cms.service.facade.common.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "大促会场优惠券查询实体类")
public class HotSaleSpInfoThirdQueryDTO implements Serializable {
    @ApiModelProperty("营销编码")
    private Long promotionCode;
    @ApiModelProperty("优惠券Id")
    private Long couponSysId;
    @ApiModelProperty("优惠券名称")
    private String couponName;
    @ApiModelProperty("活动名称")
    private String activityName;
    @ApiModelProperty("活动品牌")
    private String activityBrandName;
    @ApiModelProperty("活动开始时间")
    private Long activityStartTime;
    @ApiModelProperty("活动结束时间")
    private Long activityEndTime;
    @ApiModelProperty("营销状态")
    private Integer promotionStatus;
    @ApiModelProperty("当前页")
    private int pageIndex =1;
    @ApiModelProperty("页面大小")
    private int pageSize=20;
    @ApiModelProperty("场景")
    private Integer scene;

    @ApiModelProperty("优惠券用途")
    private Integer couponUse;

}
