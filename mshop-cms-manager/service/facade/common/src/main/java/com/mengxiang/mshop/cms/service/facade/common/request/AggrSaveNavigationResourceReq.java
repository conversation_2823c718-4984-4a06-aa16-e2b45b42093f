package com.mengxiang.mshop.cms.service.facade.common.request;


import com.mengxiang.mshop.cms.service.facade.common.result.CategorySwitchResp;
import com.mengxiang.mshop.cms.service.facade.common.result.NavigationResourceResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AggrSaveNavigationResourceReq {

    @ApiModelProperty(value = "平台页面数据")
    List<CategorySwitchReq> categorySwitchList;

    @ApiModelProperty(value = "导航数据")
    List<NavigationResourceReq> navigationResourceReqslist;
}
