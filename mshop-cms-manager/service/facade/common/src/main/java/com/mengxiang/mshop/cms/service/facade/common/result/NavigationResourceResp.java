package com.mengxiang.mshop.cms.service.facade.common.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class NavigationResourceResp {
    @ApiModelProperty(value = "导航名称")
    private String categoryName;

    @ApiModelProperty(value = "跳转目标类型：页面:PAGE、链接:LINK、PRODUCT:商品、ACTIVITY:档期 ")
    private String targetType;
    @ApiModelProperty(value = "业务单号id、链接地址值、url")
    private String targetId;

    @ApiModelProperty(value = "状态  ON || OFF")
    private String resourceSwitch;

    @ApiModelProperty(value = "跳转目标名称 ")
    private String targetName;

    @ApiModelProperty(value = "排序字段")
    private Integer orderValue;

    @ApiModelProperty(value = "导航来源类型 1=自定义导航 2=平台页面导航 ")
    private String  sourceType;
}
