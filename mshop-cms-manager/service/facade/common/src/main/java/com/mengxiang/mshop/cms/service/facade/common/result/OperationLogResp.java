package com.mengxiang.mshop.cms.service.facade.common.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 操作记录响应对象
 */
@Data
public class OperationLogResp {

    @ApiModelProperty("行为【创建、编辑、发布】")
    private String action;

    @ApiModelProperty("remark 备注")
    private String remark;

    @ApiModelProperty("业务编号")
    private String bizCode;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("操作后数据")
    private String afterData;

    @ApiModelProperty("操作前数据")
    private String beforeData;

    @ApiModelProperty("业务类型")
    private Integer bizType;

    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("创建人类型")
    private String ownerType;
}
