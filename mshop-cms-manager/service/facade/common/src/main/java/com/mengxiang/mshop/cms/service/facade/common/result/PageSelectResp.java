package com.mengxiang.mshop.cms.service.facade.common.result;

//import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 分页查询返回页面信息
 */
@Data
public class PageSelectResp {

    @ApiModelProperty("页面编号")
    private String pageCode;

    @ApiModelProperty("页面名称")
    private String name;

    @ApiModelProperty("版本号")
    private String version;

    @ApiModelProperty("页面标题")
    private String title;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("页面副标题")
    private String subTitle;

    @ApiModelProperty("修改人")
    private String updateBy;

    @ApiModelProperty("页面状态：1.待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布（审批通过）5.页面失效")
    private Integer status;

    @ApiModelProperty("业务类型 1=代发 2=批发.")
    private Integer bizType;

    @ApiModelProperty("是否删除 0 否 1是.")
    private Integer deleteFlag;

//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("修改时间")
    private String updateTime;

    @ApiModelProperty("是否有待发布版本 0 否 1是.")
    private Integer publishFlag;

    @ApiModelProperty("页面类型enum,PageType:HOME=首页,SHOP=店铺主页,MARKET=会场,CATEGORY=分类页,MICRO=微页面")
    private String pageType;

    @ApiModelProperty("页面发布失败原因")
    private String failMessage;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;


    @ApiModelProperty("所属者ID")
    private String ownerId;


    @ApiModelProperty("所属者类型（tenant:租户 shop:店铺 mengxiang:饷店 system:系统）.")
    private String ownerType;
}
