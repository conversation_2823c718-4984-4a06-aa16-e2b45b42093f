package com.mengxiang.mshop.cms.service.facade.common.request;

import com.mengxiang.mshop.aggr.core.model.req.product.ProductInfoCommonReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProductReq {

    @ApiModelProperty("当前页")
    private Integer pageIndex = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "查询的商品id列表")
    private List<ProductInfoCommonReq> productIdList;

    @ApiModelProperty(value = "规则id")
    private Integer ruleId;

    @ApiModelProperty(value = "商家店铺code")
    private String shopCode;
    
    @ApiModelProperty("上一页最后一个SPU，用于确定起始位置")
    private String lastActivitySpuCode;

    @ApiModelProperty("起始位置")
    private Integer from;

    @ApiModelProperty(value = "营销类型")
    private String promotionType;

}
