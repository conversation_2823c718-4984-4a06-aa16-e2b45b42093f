package com.mengxiang.mshop.cms.service.facade.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ActivityReq {

    @ApiModelProperty("当前页")
    private Integer pageIndex = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "查询的活动id列表")
    private List<String> activityIdList;

    @ApiModelProperty(value = "0=不走搜索，1=走搜索 默认0")
    private Integer searchType = 0;

    @ApiModelProperty(value = "档期名称")
    private String activityName;

    @ApiModelProperty(value = "品牌名")
    private String brandName;

    @ApiModelProperty(value = "档期状态 1: 预告 2: 直播  null:全部")
    private Integer activityStatus;

    @ApiModelProperty(value = "档期开始时间，起始，格式yyyy-MM-dd HH:mm:ss，null表示不限制")
    private String beginTimeStr;

    @ApiModelProperty(value = "档期结束时间，截至，格式yyyy-MM-dd HH:mm:ss，null表示不限制")
    private String endTimeStr;

    @ApiModelProperty(value = "商家店铺code")
    private String shopCode;
}
