package com.mengxiang.mshop.cms.service.facade.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 保存操作日志
 */
@Data
public class OperationLogSaveReq {

    @ApiModelProperty(value = "操作类型 SAVE,PUBLISH,DISABLED,EXECUTORY,EDIT ",required = true)
    private String action;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "业务编号",required = true)
    private String bizCode;

    @ApiModelProperty(value = "创建人",required = true)
    private String createBy;

    @ApiModelProperty(value = "操作后数据,业务数据json字符串")
    private String afterData;

    @ApiModelProperty(value = "操作前数据,业务数据json字符串")
    private String beforeData;

    @ApiModelProperty(value = "业务类型 1:页面操作记录",required = true)
    private Integer bizType;

    @ApiModelProperty("创建人ID")
    private String createUserId;

    @ApiModelProperty(value = "创建人类型")
    private String ownerType;
}
