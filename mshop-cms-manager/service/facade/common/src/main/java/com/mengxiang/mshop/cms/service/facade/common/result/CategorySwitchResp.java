package com.mengxiang.mshop.cms.service.facade.common.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("资源位下类目开关对象")
public class CategorySwitchResp {
    @ApiModelProperty("类目id")
    private String categoryId;
    @ApiModelProperty("类目名称")
    private String categoryName;
    @ApiModelProperty("开关：ON/OFF")
    private String resourceSwitch;
    @ApiModelProperty("排序字段")
    private String orderValue;
    @ApiModelProperty(value = "导航来源类型 1=自定义导航 2=平台页面导航 ")
    private String  sourceType;
}

