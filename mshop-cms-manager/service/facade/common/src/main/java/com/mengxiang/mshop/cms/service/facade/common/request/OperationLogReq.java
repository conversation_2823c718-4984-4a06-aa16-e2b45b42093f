package com.mengxiang.mshop.cms.service.facade.common.request;

import com.mengxiang.base.common.model.request.PagingRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 组件创建
 * <AUTHOR>
 */
@Data
public class OperationLogReq extends PagingRequest {

    @ApiModelProperty(value = "业务编号",required = true)
    private String bizCode;
    
    @ApiModelProperty(value = "业务类型:OperationLogBizTypeEnum",required = true)
    private Integer bizType;

    @ApiModelProperty(value = "行为")
    private String action;

}
