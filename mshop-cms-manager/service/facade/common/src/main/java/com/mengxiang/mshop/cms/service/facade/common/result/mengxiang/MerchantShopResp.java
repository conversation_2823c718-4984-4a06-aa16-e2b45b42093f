package com.mengxiang.mshop.cms.service.facade.common.result.mengxiang;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023/6/20
 * @Description: 商家店铺信息
 */
@Data
public class MerchantShopResp {

    @ApiModelProperty("商家id")
    private Long merId;
    @ApiModelProperty("商家code")
    private String merCode;
    @ApiModelProperty("商家名称")
    private String merName;
    @ApiModelProperty("店铺code")
    private String shopCode;
    @ApiModelProperty("店铺名称")
    private String shopName;
    @ApiModelProperty("店铺类型")
    private Integer shopType;
    @ApiModelProperty("店铺类型描述")
    private String shopTypeDesc;
    @ApiModelProperty("店铺logo")
    private String shopLogo;
    @ApiModelProperty("店铺状态")
    private Integer shopStatus;
    @ApiModelProperty("店铺状态描述")
    private String shopStatusDesc;
}
