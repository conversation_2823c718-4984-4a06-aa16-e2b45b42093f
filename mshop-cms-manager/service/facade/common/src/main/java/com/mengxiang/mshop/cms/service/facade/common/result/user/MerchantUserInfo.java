package com.mengxiang.mshop.cms.service.facade.common.result.user;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 商家用户信息
 */
@Data
public class MerchantUserInfo {
    
    @ApiModelProperty(
            value = "用户ID",
            dataType = "long",
            example = "1130747373572689921"
    )
    private Long userId;
    private Integer userType;
    @ApiModelProperty(
            value = "用户名",
            dataType = "string",
            example = "张三"
    )
    @NotBlank(
            message = "用户名不能为空"
    )
    private String username;
    @ApiModelProperty(
            value = "用户昵称",
            dataType = "string",
            example = "张三"
    )
    private String nickName;
    @ApiModelProperty(
            value = "密码",
            dataType = "string",
            example = "adc@123"
    )
    @NotBlank(
            message = "密码不能为空"
    )
    private String password;
    private String confirmPassword;
    private String confirmpwd;
    @ApiModelProperty(
            value = "注册邮箱",
            dataType = "string",
            example = "<EMAIL>"
    )
    @NotBlank(
            message = "邮箱不能为空"
    )
    @Email(
            message = "邮箱格式不正确"
    )
    private String email;
    @ApiModelProperty(
            value = "手机号",
            dataType = "string",
            example = "11111111111"
    )
    private String mobile;
    private String mobileEncrypt;
    @ApiModelProperty(
            value = "状态  0：禁用   1：正常",
            dataType = "int",
            example = "1"
    )
    private Integer status;
    @ApiModelProperty(
            value = "角色ID列表",
            dataType = "array",
            example = "[1, 2]"
    )
    private List<Long> roleIdList;
    @ApiModelProperty(
            value = "创建者ID",
            dataType = "Long",
            example = "1130747373572689921"
    )
    private Long createUserId;
    @ApiModelProperty(
            value = "创建时间",
            dataType = "date",
            example = "2019-05-21 12:00:00"
    )
    private Date createdTime;
    @ApiModelProperty(
            value = "创建人",
            dataType = "string",
            example = "张三"
    )
    private String createdBy;
    @ApiModelProperty(
            value = "更新时间",
            dataType = "date",
            example = "2019-05-21 12:00:00"
    )
    private Date updatedTime;
    @ApiModelProperty(
            value = "更新人",
            dataType = "string",
            example = "张三"
    )
    private String updatedBy;
    @ApiModelProperty(
            value = "部门ID",
            dataType = "Long",
            example = "68"
    )
    private Long deptId;
    @ApiModelProperty(
            value = "部门名称",
            dataType = "string",
            example = "权健集团"
    )
    private String deptName;
    @ApiModelProperty(
            value = "商户ID",
            dataType = "Long",
            example = "1130747373572689921"
    )
    private Long merchantId;
    @ApiModelProperty(
            value = "商户编号",
            dataType = "string",
            example = "M1111112252"
    )
    private String merchantCode;
    @ApiModelProperty(
            value = "商户名称",
            dataType = "string",
            example = "权健集团"
    )
    private String merchantName;
    @ApiModelProperty(
            value = "商家的审核状态",
            dataType = "int",
            example = "1"
    )
    private Integer merchantAuditStatus;
    @ApiModelProperty(
            value = "是否主账号: 0 不是 1 是",
            dataType = "int",
            example = "1"
    )
    private Integer isMain;
    @ApiModelProperty(
            value = "是否绑定手机: 0 不是 1 是",
            dataType = "int",
            example = "1"
    )
    private Integer mobileBind;
    @ApiModelProperty(
            value = "1：已删除；0：未删除",
            dataType = "int",
            example = "1"
    )
    private String isDelete;
    @ApiModelProperty(
            value = "删除时间",
            dataType = "date",
            example = "2019-05-21 12:00:00"
    )
    private Date deletedTime;
    @ApiModelProperty(
            value = "删除人",
            dataType = "string",
            example = "张三"
    )
    private String deletedBy;
    @ApiModelProperty(
            value = "验证码",
            dataType = "string",
            example = "886588"
    )
    private String authCode;

    @ApiModelProperty(
            value = "微信公众号openid",
            dataType = "string",
            example = "o6_bmjrPTlm6_2sgVt7hMZOPfL2M"
    )
    private String wechatId;
    @ApiModelProperty(
            value = "是否绑定微信公众号",
            dataType = "string",
            example = "是或否"
    )
    private String isBindWeChat;
    @ApiModelProperty("token")
    private String token;
    @ApiModelProperty(
            value = "客服IM akId",
            dataType = "string",
            example = "14d5806b945e4634925683d82d63d478"
    )
    private String akId;
    @ApiModelProperty(
            value = "上次登录ip地址",
            dataType = "string",
            example = "**************"
    )
    private String lastLoginIp;
 
    @ApiModelProperty(
            value = "上次登录时间",
            dataType = "date",
            example = "2019-05-21 12:00:00"
    )
    private Date lastLoginTime;
    @ApiModelProperty(
            value = "店铺code",
            dataType = "string",
            example = "S123456"
    )
    private String shopCode;
    @ApiModelProperty(
            value = "店铺名称",
            dataType = "string",
            example = "S123456"
    )
    private String shopName;
    @ApiModelProperty("是否灰度0否1是")
    private Integer grayRelease;

}
