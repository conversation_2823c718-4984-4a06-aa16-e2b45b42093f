package com.mengxiang.mshop.cms.service.facade.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PromotionActivityReq {

    @ApiModelProperty("营销活动版本 必传 NEW= 新版本 OLD= 老版本")
    private String promoActivityVersion;

    @ApiModelProperty("营销活动所属方 PLATFORM 平台券 MERCHANT 商家券")
    private String promoActivityOwner;
    @ApiModelProperty("营销活动所属方ID")
    private String promoActivityOwnerId;
    @ApiModelProperty("活动名称")
    private String promoActivityName;

    @ApiModelProperty("活动状态 ,新版本枚举值 NOT_BEGIN:未开始,ADVANCE:预告中,BEGIN:直播中,END:已结束,CANCEL：已作废" +
            "                   老版本枚举值 [0:未开始,1:进行中,2:已暂停,3:已结束,4:已删除]")
    private String activityStatus;

    @ApiModelProperty("营销活动ID")
    private String promoActivityId;

    @ApiModelProperty("活动开始时间开始(毫秒")
    private Long promoActivityStartTimeStart;
    @ApiModelProperty("活动开始时间结束(毫秒")
    private Long promoActivityStartTimeEnd;
    @ApiModelProperty("活动结束时间开始(毫秒")
    private Long promoActivityEndTimeStart;
    @ApiModelProperty("活动结束时间结束(毫秒")
    private Long promoActivityEndTimeEnd;
    @ApiModelProperty("页数")
    private Integer pageSize;
    @ApiModelProperty("页码")
    private Integer pageIndex;
    @ApiModelProperty("是否是查询积分兑换优惠券,默认false")
    private Boolean pointsCoupon = Boolean.FALSE;
}