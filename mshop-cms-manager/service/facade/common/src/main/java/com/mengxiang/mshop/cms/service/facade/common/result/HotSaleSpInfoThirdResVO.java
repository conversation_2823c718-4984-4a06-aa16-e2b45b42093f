package com.mengxiang.mshop.cms.service.facade.common.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "大促会场优惠券结果实体类")
public class HotSaleSpInfoThirdResVO implements Serializable {
    private static final Long serialVersionUID = -5238183694761938957L;
    @ApiModelProperty("营销编码")
    private String promotionCode;
    @ApiModelProperty("优惠券ID")
    private String couponSysId;
    @ApiModelProperty("营销状态")
    private Integer promotionStatus;
    @ApiModelProperty("营销状态描述")
    private String promotionStatusDesc;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("券名称")
    private String couponName;
    @ApiModelProperty("券面额")
    private BigDecimal couponAmount;
    @ApiModelProperty("券门槛")
    private BigDecimal couponLimitAmount;
    @ApiModelProperty("初始数量")
    private Integer initialNum;
    @ApiModelProperty("当前数量")
    private Integer currentNum;
    @ApiModelProperty("活动ID")
    private String activityId;
    @ApiModelProperty("活动名称")
    private String activityName;
    @ApiModelProperty("活动品牌Id")
    private String activityBrandId;
    @ApiModelProperty("活动品牌名称")
    private String activityBrandName;
    @ApiModelProperty("活动开始时间")
    private Date activityStartTime;
    @ApiModelProperty("活动结束时间")
    private Date activityEndTime;
    @ApiModelProperty("业务方")
    private String theBusinessSide;
    @ApiModelProperty("平台券使用开始时间描述")
    private String useStartTimeStr;
    @ApiModelProperty("业务方描述")
    private String shareTimeDesc;
    @ApiModelProperty(value = "券关联活动id")
    private String spActivityId;
    @ApiModelProperty(value = "承担方[0:平台承担, 1:商家分摊]")
    private Integer payer;
}
