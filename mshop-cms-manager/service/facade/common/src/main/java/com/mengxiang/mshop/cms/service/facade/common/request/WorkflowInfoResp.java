package com.mengxiang.mshop.cms.service.facade.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 业务单据提交时候-发起工作流请求-工作流
 */
@Data
public class WorkflowInfoResp {

    /**
     * 页面编号
     */
    @ApiModelProperty("页面编号")
    private String pageCode;

    /**
     * 生效版本号
     */
    @ApiModelProperty("版本号")
    private String version;
    /**
     * 业务key
     */
    @ApiModelProperty("业务key")
    private String businessKey;


    /**
     * 流程编码
     */
    @ApiModelProperty("流程编码")
    private String procInstCode;
}
