package com.mengxiang.mshop.cms.service.facade.common.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mengxiang.base.common.model.request.PagingRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


/**
 * 页面分页条件查询对象
 */
@Data
public class PageSearchReq extends PagingRequest {

    @ApiModelProperty("页面编号集合")
    private List<String> pageCodeList;

    @ApiModelProperty("页面名称")
    private String name;

    @ApiModelProperty("版本号")
    private String version;

    @ApiModelProperty("页面标题")
    private String title;

    @ApiModelProperty("租户id.")
    private String tenantId;

    @ApiModelProperty("属者类型（tenant:租户 shop:店铺 mengxiang:饷店）.")
    private String ownerType;

    @ApiModelProperty("属者ID")
    private String ownerId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("页面副标题")
    private String subTitle;

    @ApiModelProperty("修改人")
    private String updateBy;

    @ApiModelProperty("PageInstanceStatusEnum 页面状态：1.待发布（草稿可编辑）2.审批中 3.审批驳回 4.已发布（审批通过）.")
    private Integer status;

    @ApiModelProperty("端：h5、app 、小程序")
    private String channel;

    @ApiModelProperty(" type 类型：首页、商品详情页、会场、微页面.")
    private String type;

    @ApiModelProperty(" type 类型：首页、商品详情页、会场、微页面.")
    private List<String> typeList;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间-开始")
    private Date createTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间-结束")
    private Date createTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("修改时间-开始")
    private Date updateTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("修改时间-结束")
    private Date updateTimeEnd;

    @ApiModelProperty("查询类型 空=跟风火轮登陆用户一致 1=管理员权限")
    private Integer searchType;

}
