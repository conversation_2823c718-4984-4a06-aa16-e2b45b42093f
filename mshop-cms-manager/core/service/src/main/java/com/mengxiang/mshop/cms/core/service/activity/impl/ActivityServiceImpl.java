package com.mengxiang.mshop.cms.core.service.activity.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.mshop.aggr.core.model.req.activity.ActivityListReq;
import com.mengxiang.mshop.aggr.core.model.req.activity.ActivitySearchReq;
import com.mengxiang.mshop.aggr.core.model.req.base.AggrFeignBaseDTO;
import com.mengxiang.mshop.aggr.core.model.vo.activity.ActivityListVO;
import com.mengxiang.mshop.cms.common.service.integration.feign.ActivityClient;
import com.mengxiang.mshop.cms.core.service.activity.ActivityService;
import com.mengxiang.mshop.cms.core.service.converter.AggrFeignConverter;
import com.mengxiang.mshop.cms.service.facade.common.request.ActivityReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ActivityServiceImpl implements ActivityService {

    @Autowired
    private ActivityClient activityClient;

    @Autowired
    private AggrFeignConverter aggrFeignConverter;

    /**
     * 查询活动列表
     * @param activityReq
     * @return
     */
    @Override
    public Pagination<ActivityListVO> activityList(ActivityReq activityReq){
        AggrFeignBaseDTO<ActivityListReq> req = new AggrFeignBaseDTO<>();
        aggrFeignConverter.setAggrFeignBaseDTO(req);
        ActivityListReq productListReq = new ActivityListReq();
        ActivitySearchReq searchReq = new ActivitySearchReq();
        if (CollectionUtil.isNotEmpty(activityReq.getActivityIdList())) {
            if(activityReq.getSearchType() == 1){
                //1=走搜索
                searchReq.setAllowActivityIds(activityReq.getActivityIdList());
                searchReq.setIsNeedSpuIds(Boolean.TRUE);
            }else{
                //0=不走搜索
                productListReq.setActivityIds(activityReq.getActivityIdList());
                searchReq.setIsNeedSpuIds(Boolean.FALSE);

            }
        }
        searchReq.setPageNum(activityReq.getPageIndex());
        searchReq.setPageSize(activityReq.getPageSize());
        searchReq.setActivityStatus(activityReq.getActivityStatus());
        searchReq.setActivityNameKeyword(activityReq.getActivityName());
        searchReq.setBrandNameKeyword(activityReq.getBrandName());
        searchReq.setStartTimeFrom(activityReq.getBeginTimeStr());
        searchReq.setEndTimeTo(activityReq.getEndTimeStr());
        searchReq.setShopCodes(Lists.newArrayList(activityReq.getShopCode()));
        productListReq.setSearchParams(searchReq);
        req.setBusinessData(productListReq);
        return activityClient.activityList(req);
    }
}
