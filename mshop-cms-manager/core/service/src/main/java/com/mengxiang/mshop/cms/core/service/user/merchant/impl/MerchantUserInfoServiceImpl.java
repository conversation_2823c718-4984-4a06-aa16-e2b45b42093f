package com.mengxiang.mshop.cms.core.service.user.merchant.impl;

import com.alibaba.fastjson.JSONObject;
import com.mengxiang.merchant.platform.query.token.entity.SysUserDTO;
import com.mengxiang.mshop.cms.core.service.user.merchant.MerchantUserInfoService;
import com.mengxiang.mshop.cms.common.service.integration.feign.merchant.MerchantLoginInfoClient;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 商家用户信息
 * <AUTHOR>
 */
@Service
@Slf4j
public class MerchantUserInfoServiceImpl implements MerchantUserInfoService {
    
    @Autowired
    private MerchantLoginInfoClient loginInfoClient;
    
    @Override
    public MerchantUserInfo queryUserInfoByToken(HttpServletRequest request) {
        //String token = request.getHeader("token");
        //if(StringUtils.isEmpty(token)){
        //    return null;
        //}
        SysUserDTO sysUserDTO = loginInfoClient.getLoginInfoByRequest(request);
        if(Objects.isNull(sysUserDTO)){
            return null;
        }
        MerchantUserInfo merchantUserInfo = new MerchantUserInfo();
        if (StringUtils.isEmpty(sysUserDTO.getShopCode())) {
            String shopCode = replace(sysUserDTO.getMerchantCode());
            merchantUserInfo.setShopCode(shopCode);
        } else {
            merchantUserInfo.setShopCode(sysUserDTO.getShopCode());
        }
        merchantUserInfo.setUserId(sysUserDTO.getUserId());
        merchantUserInfo.setMerchantId(sysUserDTO.getMerchantId());
        merchantUserInfo.setMerchantCode(sysUserDTO.getMerchantCode());
        merchantUserInfo.setUsername(sysUserDTO.getUsername());
        if (StringUtils.isEmpty(merchantUserInfo.getShopCode())){
            log.warn("sysUserDTO shopCode is null, sysUserDTO:{}", JSONObject.toJSONString(merchantUserInfo));
            return null;
        }
        return merchantUserInfo;
    }

    private String replace(String merCode) {
        return StringUtils.isEmpty(merCode) ? null : merCode.replace("M", "S");
    }
}
