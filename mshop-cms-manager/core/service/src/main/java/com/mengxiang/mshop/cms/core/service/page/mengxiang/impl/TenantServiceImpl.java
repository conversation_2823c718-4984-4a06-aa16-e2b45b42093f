package com.mengxiang.mshop.cms.core.service.page.mengxiang.impl;

import com.akucun.base.shorturl.stub.GenerateUrlVO;
import com.akucun.base.shorturl.stub.StorageDTO;
import com.akucun.mshop.gateway.wx.stub.others.dto.req.WeChatUnlimitShortUrlRequest;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.base.share.service.facade.common.enums.CarrierEnum;
import com.mengxiang.base.share.service.facade.common.enums.TargetChannelEnum;
import com.mengxiang.base.share.service.facade.common.request.GenerateShortCodeLinkRequest;
import com.mengxiang.base.share.service.facade.common.result.GenerateShortCodeLinkVO;
import com.mengxiang.mshop.cms.common.service.integration.feign.PageClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.TenantFeignClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.WeChantFeignClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.share.ShortCodeServiceClient;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.service.page.TenantService;
import com.mengxiang.mshop.cms.service.facade.common.request.PointsPageConfigReq;
import com.mengxiang.mshop.cms.service.facade.common.request.TenantUser;
import com.mengxiang.mshop.cms.service.facade.common.result.PageSelectResp;
import com.mengxiang.mshop.cms.service.facade.common.result.PointsPageConfigResp;
import com.mengxiang.saas.service.facade.common.request.tenant.TenantComponentReq;
import com.mengxiang.saas.service.facade.common.response.wechat.AppAuthorizationInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class TenantServiceImpl implements TenantService{

    @Autowired
    private WeChantFeignClient weChantFeignClient;

    @Autowired
    private TenantFeignClient tenantFeignClient;

    @Resource
    private PageClient pageClient;

    @Autowired
    private ShortCodeServiceClient shortCodeServiceClient;

    @Value("${saas.micro.mini.url:pages/home-saas-sec/preview?customPageCode=}")
    private String microUrlByMini;

    @Value("${saas.micro.mini.preview.page:pages/home-saas/preview}")
    private String miniPreviewPage;

    @Override
    public String preview(Long tenantId, String pageCode, String version) {
        WeChatUnlimitShortUrlRequest weChatUnlimitShortUrlRequest = new WeChatUnlimitShortUrlRequest();
        AppAuthorizationInfoResp authorizationInfoResp = tenantFeignClient.queryAuthorizationInfo(tenantId);
        if (Objects.isNull(authorizationInfoResp)) {
            log.error("preview authorizationInfoResp is null req:{},{},{}", tenantId, pageCode, version);
            return "";
        }
        weChatUnlimitShortUrlRequest.setAppid(authorizationInfoResp.getAuthorizerAppid());
        weChatUnlimitShortUrlRequest.setPage(miniPreviewPage);
        //生成短码
        StorageDTO storageDTO = new StorageDTO();
        storageDTO.setSource("akucun-crm-manager");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pageCode", pageCode);
        jsonObject.put("version", version);
        jsonObject.put("preview", true);
        storageDTO.setUrl(jsonObject.toString());
        GenerateUrlVO generateUrlVO = weChantFeignClient.genShortV2(storageDTO);
        if (Objects.isNull(generateUrlVO)) {
            log.error("preview generateUrlVO is null req:{}", JSON.toJSONString(storageDTO));
            return "";
        }
        weChatUnlimitShortUrlRequest.setScene(generateUrlVO.getPath());
        weChatUnlimitShortUrlRequest.setCheckPath(Boolean.FALSE);
        return weChantFeignClient.genUnLimitShortLinkPic(weChatUnlimitShortUrlRequest);
    }

    @Override
    public Map<String, String> generatePageUrl(String pageCode, Long tenantId) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(pageCode), "pageCode 不能为空");
        String miniUrl = microUrlByMini + pageCode;
        Map<String, String> resp = new HashedMap();
        resp.put("MINI", miniUrl);
        resp.put("HTTPMINILINK", getTenantMiniLink(tenantId, pageCode));
        return resp;
    }

    //获取小程序链接
    private String getTenantMiniLink(Long tenantId, String pageCode) {
        GenerateShortCodeLinkRequest req = new GenerateShortCodeLinkRequest();
        req.setTargetChannel(TargetChannelEnum.MINI.getCode());
        req.setCarrier(CarrierEnum.URL_LINK.getCode());
        req.setContentType("SAAS_STORE");
        req.setContentId(pageCode);
        //页面参数
        Map<String, String> shortCodeExtra = Maps.newHashMap();
        shortCodeExtra.put("customPageCode", pageCode);
        req.setShortCodeExtra(shortCodeExtra);

        com.mengxiang.base.share.service.facade.common.request.UserInfo userInfo = new com.mengxiang.base.share.service.facade.common.request.UserInfo();
        userInfo.setTenantId(tenantId);
        req.setUserInfo(userInfo);
        GenerateShortCodeLinkVO link = shortCodeServiceClient.generateShortCodeLink(req);
        if (null != link) {
            return link.getUrl();
        }
        return "";
    }


    @Override
    public PointsPageConfigResp searchPointsPageConfig(TenantUser tenantUser) {
        PointsPageConfigResp pointsPageConfigResp = new PointsPageConfigResp();
        Object pointsPageConfig = tenantFeignClient.queryComponentValue(tenantUser.getTenantId(), "pointsPageConfig");
        if (Objects.nonNull(pointsPageConfig)) {
            PointsPageConfigReq req =  JSON.parseObject(String.valueOf(pointsPageConfig),PointsPageConfigReq.class);
            pointsPageConfigResp.setPointsPageInletImgUrl(req.getPointsPageInletImgUrl());
            if (Objects.nonNull(req.getPageCode())) {
                Result<PageBO> pageBOResult = pageClient.detail(req.getPageCode());
                if (Objects.nonNull(pageBOResult) && pageBOResult.isSuccess() && Objects.nonNull(pageBOResult.getData())) {
                    pointsPageConfigResp.setPointsPage(buildPageSelect(pageBOResult.getData()));
                }
            }
        }
        return pointsPageConfigResp;
    }

    @Override
    public void updatePointsPageConfig(TenantUser tenantUser, PointsPageConfigReq req) {
        List<TenantComponentReq> tenantComponentReqList = Lists.newArrayList();
        TenantComponentReq componentReq = new TenantComponentReq();
        componentReq.setComponentKey("pointsPageConfig");
        componentReq.setComponentValue(JSON.toJSONString(req));
        tenantComponentReqList.add(componentReq);
        tenantFeignClient.updateComponent(tenantUser.getTenantId(), tenantComponentReqList);
    }


    private PageSelectResp buildPageSelect(PageBO pageBO) {
        PageSelectResp resp = new PageSelectResp();
        resp.setPageCode(pageBO.getPageCode());
        resp.setName(pageBO.getName());
        resp.setTitle(pageBO.getTitle());
        resp.setVersion(pageBO.getVersion());
        return resp;
    }
}
