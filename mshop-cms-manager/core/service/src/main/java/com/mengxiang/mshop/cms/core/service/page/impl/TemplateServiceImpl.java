package com.mengxiang.mshop.cms.core.service.page.impl;


import com.mengxiang.mshop.cms.common.service.integration.feign.TemplateClient;
import com.mengxiang.mshop.cms.core.model.result.PageTemplateResult;
import com.mengxiang.mshop.cms.core.service.page.TemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 页面组件服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TemplateServiceImpl implements TemplateService {

    @Resource
    private TemplateClient templateClient;

    /**
     * 模版列表
     *
     * @param ownerId
     * @param ownerType
     * @param pageUseType
     * @return
     */
    @Override
    public List<PageTemplateResult> list(String ownerId, String ownerType, String pageUseType, Integer version) {
        if (Objects.equals(1, version)) {
            pageUseType = pageUseType+"V3";
        }
        return templateClient.list(ownerId, ownerType, pageUseType);
    }

    /**
     * 页面模版详情
     *
     * @param templateCode
     * @return
     */
    @Override
    public PageTemplateResult detail(String templateCode) {
        return templateClient.detail(templateCode);
    }
}
