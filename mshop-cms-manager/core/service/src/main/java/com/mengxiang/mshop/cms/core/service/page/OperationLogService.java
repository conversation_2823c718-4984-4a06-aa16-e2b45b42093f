package com.mengxiang.mshop.cms.core.service.page;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.service.facade.common.request.OperationLogReq;
import com.mengxiang.mshop.cms.service.facade.common.request.OperationLogSaveReq;
import com.mengxiang.mshop.cms.service.facade.common.result.OperationLogResp;

/**
 * 操作记录服务
 */
public interface OperationLogService {
    /**
     * 分页查询
     * @return
     */
    Result<Pagination<OperationLogResp>> operationLogPage(OperationLogReq req);

    Result<Boolean> saveOperationLog(OperationLogSaveReq req);

}
