package com.mengxiang.mshop.cms.core.service.page;

import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.SellRuleGetBatchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.service.facade.common.request.PageSearchReq;
import com.mengxiang.mshop.cms.service.facade.common.result.PageSelectResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.PageMengXiangPrewLink;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;

import java.util.List;
import java.util.Map;


/**
 * 页面组件服务
 * <AUTHOR>
 */
public interface PageService {


    /**
     * 页面保存/发布
     * @param savePageRequest
     * @return
     */
    Result<PageBO> save(SavePageRequest savePageRequest);

    Result<PageBO> saveByMengXiang(SavePageRequest req, UserInfo userInfo);

    /**
     * 页面保存/发布（商家）
     * @param savePageRequest
     * @return
     */
    Result<PageBO> saveByMer(SavePageRequest savePageRequest, MerchantUserInfo userInfo);

    /**
     * 失效页面
     * @param pageCode
     * @param version
     * @return
     */
    Result<Boolean> setPageInvalidation(String pageCode,String version,String updateBy, String updateUserId);

    /**
     * 页面信息
     * @param pageCode
     * @param version
     * @return
     */
    Result<PageBO> detail(String pageCode, String version);

    /**
     * 使用模版
     * @param templateCode
     * @return
     */
    Result<PageBO> detailByTemplate(String templateCode,String ownerId, String ownerType);

    /**
     * 恢复至上一发布版本
     * @param pageCode
     * @return
     */
    Result<PageBO> detailToBeforePublished(String pageCode);


    /**
     * 预览
     * @param ownerType
     * @param pageCode
     * @param role
     * @return
     */
    Result<String> preview(String ownerType,String pageCode,String role);
    /**
     * 预览
     * @param ownerType
     * @param pageCode
     * @param role
     * @return
     */
    Map<String, PageMengXiangPrewLink> previewMap(String ownerType, String pageCode, String role);

    Map<String, PageMengXiangPrewLink> previewShop(String staffId, String pageCode, String version,String merShopCode);
    /**
     * 复制并创建新页面
     * @param pageCode
     * @param version
     * @return
     */
    Result<PageBO> detailToNewPage(String pageCode,String version);

    Result<Pagination<PageSelectResp>> pageSelect(PageSearchReq req);

    Result<List<PageRuleInfoResult>> getRuleBatch(SellRuleGetBatchRequest request);

    /**
     * 创建默认页面
     * @param tenantId
     * @param shopCode
     */
    Result<PageBO> createDefaultPage(String shopCode,String mCode,String operatorUserName);

    /**
     * 创建默认页面
     */
    Result<PageBO> updateMerPage(String shopCode);

    /**
     * 创建默认页面
     *
     */
    Result<PageBO> createDefaultPage(String shopCode);

    /**
     * 查询端上的的生效页面
     * @param pageCode
     * @return
     */
    Result<PageBO> pageDetailByCode(String pageCode);

    /**
     * 设置当前页面为主页
     * @param pageCode
     * @param ownerId
     * @param ownerType
     * @return
     */
    Result<Void> setPageIndex(String pageCode, String ownerId,String pageType, String ownerType);
}
