package com.mengxiang.mshop.cms.core.service.product.impl;

import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.mshop.aggr.core.model.req.base.AggrFeignBaseDTO;
import com.mengxiang.mshop.aggr.core.model.req.product.ProductListReq;
import com.mengxiang.mshop.aggr.core.model.req.product.ProductSearchReq;
import com.mengxiang.mshop.aggr.core.model.vo.product.ProductListVO;
import com.mengxiang.mshop.cms.common.service.integration.feign.PageClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.ProductClient;
import com.mengxiang.mshop.cms.core.model.enums.PromotionTypeEnum;
import com.mengxiang.mshop.cms.core.model.result.SafeModelRuleLabelResult;
import com.mengxiang.mshop.cms.core.service.converter.AggrFeignConverter;
import com.mengxiang.mshop.cms.core.service.product.ProductService;
import com.mengxiang.mshop.cms.service.facade.common.request.ProductReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductServiceImpl implements ProductService{

    @Autowired
    private ProductClient productClient;

    @Autowired
    private AggrFeignConverter aggrFeignConverter;

    @Autowired
    private PageClient pageClient;

    /**
     * 查询商品列表
     * @param productReq
     * @return
     */
    @Override
    public Pagination<ProductListVO> productList(ProductReq productReq){
        AggrFeignBaseDTO<ProductListReq> req = new AggrFeignBaseDTO<>();
        aggrFeignConverter.setAggrFeignBaseDTO(req);
        ProductListReq productListReq = new ProductListReq();
        productListReq.setProductReqList(productReq.getProductIdList());
        ProductSearchReq searchParams = new ProductSearchReq();
        searchParams.setScene("SEARCH_PRODUCT");
        searchParams.setRuleId(productReq.getRuleId());
        searchParams.setPageNum(productReq.getPageIndex());
        searchParams.setPageSize(productReq.getPageSize());
        searchParams.setShopCodes(Lists.newArrayList(productReq.getShopCode()));
        searchParams.setLastActivitySpuCode(productReq.getLastActivitySpuCode());
        searchParams.setFrom(productReq.getFrom());
        if(StringUtils.isNotBlank(productReq.getPromotionType())){
            searchParams.setPromotionTypes(PromotionTypeEnum.findMarketingByCode(productReq.getPromotionType()));
        }
        productListReq.setSearchParams(searchParams);
        req.setBusinessData(productListReq);
        if(Objects.nonNull(productReq.getRuleId())){
            return productClient.productListByRule(req);
        }else if(CollectionUtils.isNotEmpty(productReq.getProductIdList())){
            //指定spuId查询后台商品接口
            List<Long> activitySpuCodes = productReq.getProductIdList().stream().map(x->Long.valueOf(x.getActivitySpuId())).collect(Collectors.toList());
            List<ProductListVO> productListVOList = productClient.batchQuerySpuInfo(activitySpuCodes);
            return new Pagination(searchParams.getPageNum(),searchParams.getPageSize(),(long)productListVOList.size(),productListVOList);
        }else{
            return productClient.productList(req);
        }
    }



    /**
     * 查询选品中心营销标签
     */
    @Override
    public List<SafeModelRuleLabelResult> findSafeModelRuleLabel(){
        return pageClient.findSafeModelRuleLabel();
    }
}
