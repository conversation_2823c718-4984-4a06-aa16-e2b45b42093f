package com.mengxiang.mshop.cms.core.service.resource;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.service.facade.common.request.*;
import com.mengxiang.mshop.cms.service.facade.common.result.AggrDetailNavigationResourceResp;

import java.util.List;

public interface ResourceService {


    /**
     * 保存导航
     * @param navigationResourceReqList
     * @param tenantUser
     */
    void saveNavigation(List<NavigationResourceReq> navigationResourceReqList, List<CategorySwitchReq> platformCategoryList, TenantUser tenantUser);



    /**
     * 查询导航
     * @param tenantUser
     */
    List<NavigationResourceReq>  navigationDetail(TenantUser tenantUser);

    /**
     * 聚合保存导航
     * @param tenantUser
     * @param aggrSaveNavigationResourceReqs
     */
    void aggrSaveNavigation(TenantUser tenantUser, AggrSaveNavigationResourceReq aggrSaveNavigationResourceReqs);


    /**
     * 聚合查询导航
     * @param tenantUser
     * @param aggrDetailNavigationResourceReq
     */
    AggrDetailNavigationResourceResp  aggrNavigationDetail(TenantUser tenantUser, AggrDetailNavigationResourceReq aggrDetailNavigationResourceReq);
}
