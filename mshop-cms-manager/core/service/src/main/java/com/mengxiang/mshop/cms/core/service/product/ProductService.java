package com.mengxiang.mshop.cms.core.service.product;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.mshop.aggr.core.model.vo.product.ProductListVO;
import com.mengxiang.mshop.cms.core.model.result.SafeModelRuleLabelResult;
import com.mengxiang.mshop.cms.service.facade.common.request.ProductReq;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProductService {

    /**
     * 查询商品列表
     * @param productReq
     * @return
     */
    Pagination<ProductListVO> productList(ProductReq productReq);


    List<SafeModelRuleLabelResult> findSafeModelRuleLabel();

}
