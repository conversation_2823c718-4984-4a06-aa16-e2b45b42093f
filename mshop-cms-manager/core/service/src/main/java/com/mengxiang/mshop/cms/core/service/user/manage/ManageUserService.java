package com.mengxiang.mshop.cms.core.service.user.manage;


import com.aikucun.security.sso.authen.core.controller.SsoBaseController;
import com.aikucun.security.sso.authen.core.dto.response.SsoAuthenResult;
import com.aikucun.security.sso.authen.core.service.SsoAuthenticationService;
import com.aikucun.security.sso.permission.dto.base.SsoPermissionResult;
import com.aikucun.security.sso.permission.dto.request.ButtonQuery;
import com.aikucun.security.sso.permission.dto.request.MenuQuery;
import com.aikucun.security.sso.permission.dto.request.ResourceQuery;
import com.aikucun.security.sso.permission.dto.request.UserQuery;
import com.aikucun.security.sso.permission.dto.response.Button;
import com.aikucun.security.sso.permission.dto.response.Menu;
import com.aikucun.security.sso.permission.dto.response.Resource;
import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.aikucun.security.sso.permission.feign.UserPermissionClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


@Service
@Slf4j
public class ManageUserService extends SsoBaseController {
	@Autowired
	private UserPermissionClient userPermissionClient;
	@Autowired
	private SsoAuthenticationService ssoAuthenticationService;

	/**cmdb应用名*/
	@Value("${sso.client.app-id}")
	private String appId;

	/**在SSO中该应用的唯一标识*/
	@Value("${sso.client.secret}")
	private String secret;

	/**
	 * 从SSO获取登陆用户信息
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public SsoPermissionResult<UserInfo> getLoginUser(HttpServletRequest request) {
		log.info("-----进入getLoginUser方法-----");

		//从SSO获取用户信息
		UserQuery userQuery = new UserQuery();
		userQuery.setAuthentication(this.getAuthenticationValue(request));
		userQuery.setAppSecret(secret);
		userQuery.setAppId(appId);
		SsoPermissionResult<UserInfo> result = userPermissionClient.getUserInfo(userQuery);
		log.info("-----从SSO查询人员success:{}，返回信息code:{}，message:{},data:{}-----",result.getSuccess(),result.getCode(),result.getMessage(),result.getData());
		return result;
	}
	/**查询当前登录人的菜单/按钮权限*/
	public SsoPermissionResult<List<Resource>> getMenuAndButton(HttpServletRequest request){
		ResourceQuery resourceQuery = new ResourceQuery();
		resourceQuery.setAuthentication(this.getAuthenticationValue(request));
		resourceQuery.setAppSecret(secret);
		resourceQuery.setAppId(appId);
		return userPermissionClient.getResourceList(resourceQuery);
	}

	public SsoPermissionResult<List<Button>> getButtonList(Long menuId){
		ButtonQuery buttonQuery = new ButtonQuery();
		buttonQuery.setAuthentication(this.getAuthenticationValue(null));
		buttonQuery.setAppSecret(secret);
		buttonQuery.setAppId(appId);
		buttonQuery.setMenuId(menuId);
		return userPermissionClient.getButtonList(buttonQuery);
	}


	public SsoPermissionResult<List<Menu>> getMenuList(){
		MenuQuery menuQuery = new MenuQuery();
		menuQuery.setAuthentication(this.getAuthenticationValue(null));
		menuQuery.setAppSecret(secret);
		menuQuery.setAppId(appId);
		return userPermissionClient.getMenuList(menuQuery);
	}

	/***
	 * 登出接口
	 * @param request
	 * @param response
	 * @return SsoAuthenResult<String>
	 */
	public SsoAuthenResult<String> ssoLogout(HttpServletRequest request, HttpServletResponse response){
		return ssoAuthenticationService.logout(request,response);
	}
}