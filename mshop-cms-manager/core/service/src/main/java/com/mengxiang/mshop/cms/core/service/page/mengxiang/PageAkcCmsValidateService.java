package com.mengxiang.mshop.cms.core.service.page.mengxiang;

import com.akucun.cms.aggregation.stub.feign.res.ConferenceConfigRes;
import com.akucun.cms.model.vo.hotSale.HotSaleConfigVO;
import com.x.live.center.dto.LiveInfoDTO;

/**
 * <AUTHOR>
 * @Date: 2023/5/31
 * @Description:
 */
public interface PageAkcCmsValidateService {

    HotSaleConfigVO queryHotSalecInfo(Integer hotSaleId);

    ConferenceConfigRes conferenceBaseInfoAgg(Integer conferenceId);

    LiveInfoDTO queryLiveBasics(String liveRoomId);
}
