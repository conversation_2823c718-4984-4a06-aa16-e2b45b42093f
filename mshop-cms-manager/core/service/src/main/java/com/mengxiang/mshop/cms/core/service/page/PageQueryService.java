package com.mengxiang.mshop.cms.core.service.page;

import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import com.mengxiang.mshop.cms.service.facade.common.request.PageSearchReq;
import com.mengxiang.mshop.cms.service.facade.common.result.PageSelectResp;


import java.util.List;


/**
 * 内容检测
 */
public interface PageQueryService {

    PageBO detail(String pageCode, String version);

    Result<Pagination<PageSelectResp>> pageSelect(PageSearchReq req);

    Result<Pagination<PageSelectResp>> pageSelectByMengXiang(PageSearchReq req, UserInfo userInfo);

    Result<Pagination<PageSelectResp>> pageSelectV2(PageSearchReq req);
}
