package com.mengxiang.mshop.cms.core.service.page.mengxiang.impl;

import com.akucun.cms.aggregation.stub.feign.res.ConferenceConfigRes;
import com.akucun.cms.model.vo.hotSale.HotSaleConfigVO;
import com.mengxiang.mshop.cms.common.service.integration.feign.AkcCmsClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.AkcLiveClient;
import com.mengxiang.mshop.cms.core.service.page.mengxiang.PageAkcCmsValidateService;
import com.x.live.center.dto.LiveInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 老会场是否存在
 * <AUTHOR>
 */
@Service
@Slf4j
public class PageAkcCmsValidateServiceImpl implements PageAkcCmsValidateService {
    
    @Autowired
    private AkcCmsClient akcCmsClient;

    @Autowired
    private AkcLiveClient akcLiveClient;


    @Override
    public HotSaleConfigVO queryHotSalecInfo(Integer hotSaleId) {
        return akcCmsClient.queryHotSalecInfo(hotSaleId);
    }

    @Override
    public ConferenceConfigRes conferenceBaseInfoAgg(Integer conferenceId) {
        return akcCmsClient.conferenceBaseInfoAgg(conferenceId);
    }

    @Override
    public LiveInfoDTO queryLiveBasics(String liveRoomId){
        return akcLiveClient.queryLiveBasics(liveRoomId);
    }
}
