package com.mengxiang.mshop.cms.core.service.resource.impl;


import com.akucun.cms.model.dto.business.TenantResourceSwitchDTO;
import com.akucun.cms.model.vo.business.CategorySwitchVO;
import com.akucun.mshop.common.util.BeanCopyUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.service.integration.feign.AkuCunCmsClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.MshopCmsClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.ResourceClient;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.NavigationResourceBO;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.ResourceChannelEnum;
import com.mengxiang.mshop.cms.core.model.enums.ResourceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.ResourceTypeEnum;
import com.mengxiang.mshop.cms.core.service.resource.ResourceService;
import com.mengxiang.mshop.cms.service.facade.common.request.*;
import com.mengxiang.mshop.cms.service.facade.common.result.AggrDetailNavigationResourceResp;

import com.mengxiang.mshop.cms.service.facade.common.result.CategorySwitchResp;
import com.mengxiang.mshop.cms.service.facade.common.result.NavigationResourceResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ResourceServiceImpl implements ResourceService {

    @Autowired
    private ResourceClient resourceClient;

    @Autowired
    private AkuCunCmsClient akuCunCmsClient;

    @Autowired
    private MshopCmsClient mshopCmsClient;

    // 平台页面
    private final static String PLAT_PAGE_SOURCE_TYPE = "2";

    // 拥有者类型
    private final static String NAVIGATION_SOURCE_TYPE = "tenant";

    /**
     * 保存导航
     * @param navigationResourceReqList
     * @param tenantUser
     */
    @Override
    public void saveNavigation(List<NavigationResourceReq> navigationResourceReqList, List<CategorySwitchReq> platformCategoryList,TenantUser tenantUser){
        List<NavigationResourceBO.ResourceConfig> tabConfigList = navigationResourceReqList.stream().map(x->{
            NavigationResourceBO.ResourceConfig config = new NavigationResourceBO.ResourceConfig();
            config.setStatus(Objects.equals("ON",x.getResourceSwitch())? ResourceStatusEnum.PUBLISH.getCode():ResourceStatusEnum.DISABLED.getCode());
            config.setTargetId(x.getTargetId());
            config.setTargetType(x.getTargetType());
            config.setTabName(x.getCategoryName());
            config.setSourceType(x.getSourceType());
            config.setOrderValue(String.valueOf(x.getOrderValue()));
            return config;
        }).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(platformCategoryList)){
            List<NavigationResourceBO.ResourceConfig> platformConfigList = platformCategoryList.stream().map(x->{
                NavigationResourceBO.ResourceConfig config = new NavigationResourceBO.ResourceConfig();
                config.setSourceType(x.getSourceType());
                config.setOrderValue(x.getOrderValue()+"");
                config.setCategoryId(x.getCategoryId());
                return config;
            }).collect(Collectors.toList());
            tabConfigList.addAll(platformConfigList);
        }
        NavigationResourceBO navigationComponentBO = new NavigationResourceBO();
        navigationComponentBO.setTabConfigDetails(tabConfigList);
        navigationComponentBO.setTenantId(String.valueOf(tenantUser.getTenantId()));
        navigationComponentBO.setOwnerId(String.valueOf(tenantUser.getTenantId()));
        navigationComponentBO.setOwnerType(PageOwnerType.SAAS_TENANT.getOwnerType());
        navigationComponentBO.setCreateBy(tenantUser.getUserName());
        navigationComponentBO.setResourceType(ResourceTypeEnum.NAVIGATION.getCode());
        navigationComponentBO.setChannelList(Lists.newArrayList(ResourceChannelEnum.SAA_S_APP.getCode(),ResourceChannelEnum.SAA_S_APPLETS.getCode()));
        TimeConfigBO timeConfigBO = new TimeConfigBO();
        timeConfigBO.setEffectiveType(1);
        navigationComponentBO.setTimeConfig(timeConfigBO);
        Result result = resourceClient.saveNavigationResource(navigationComponentBO);
        if(Objects.isNull(result)){
            throw new BusinessException("保存导航失败");
        }
        if(!result.isSuccess()){
            throw new BusinessException(result.getMessage());
        }
    }


    /**
     * 查询导航
     * @param tenantUser
     */
    @Override
    public List<NavigationResourceReq>  navigationDetail(TenantUser tenantUser){
        List<NavigationResourceReq> resultList = Lists.newArrayList();
        NavigationResourceBO navigationResourceBO = new NavigationResourceBO();
        navigationResourceBO.setTenantId(String.valueOf(tenantUser.getTenantId()));
        navigationResourceBO.setOwnerId(String.valueOf(tenantUser.getTenantId()));
        navigationResourceBO.setOwnerType(PageOwnerType.SAAS_TENANT.getOwnerType());
        NavigationResourceBO navigationComponentBO = resourceClient.getNavigationResourceById(navigationResourceBO);
        if(Objects.nonNull(navigationComponentBO) && CollectionUtils.isNotEmpty(navigationComponentBO.getTabConfigDetails())){
            AtomicInteger index = new AtomicInteger();
            navigationComponentBO.getTabConfigDetails().forEach(x->{
                index.getAndIncrement();
                NavigationResourceReq req = new NavigationResourceReq();
                req.setCategoryName(x.getTabName());
                req.setResourceSwitch(Objects.equals(ResourceStatusEnum.PUBLISH.getCode(),x.getStatus())?"ON":"OFF");
                req.setTargetId(x.getTargetId());
                req.setTargetType(x.getTargetType());
                req.setTargetName(x.getTargetName());
                if(StringUtils.isNotBlank(x.getOrderValue())){
                    req.setOrderValue(Integer.valueOf(x.getOrderValue()));
                }else{
                    req.setOrderValue(index.get());
                }
                req.setSourceType(x.getSourceType());
                req.setCategoryId(x.getCategoryId());
                resultList.add(req);
            });
        }
        return resultList;
    }

    @Override
    public void aggrSaveNavigation(TenantUser tenantUser, AggrSaveNavigationResourceReq aggrSaveNavigationResourceReqs) {

        // 保存自定义导航
        List<NavigationResourceReq> navigationResourceReqslist = aggrSaveNavigationResourceReqs.getNavigationResourceReqslist();
        List<CategorySwitchReq> categorySwitchList = aggrSaveNavigationResourceReqs.getCategorySwitchList();
        this.saveNavigation(navigationResourceReqslist,categorySwitchList, tenantUser);

        // 批量改开关状态categoryId、resourceSwitch
        List<TenantResourceSwitchDTO> dtoList = getTenantResourceSwitchDTOS(tenantUser, aggrSaveNavigationResourceReqs);
        dtoList.forEach(akuCunCmsClient::updateTenantResourceSwitch);
    }

    private static TenantResourceSwitchDTO getTenantResourceSwitchDTO(TenantUser tenantUser) {
        TenantResourceSwitchDTO switchDTO = new TenantResourceSwitchDTO();
        switchDTO.setTenantUserId(String.valueOf(tenantUser.getUserId()));
        switchDTO.setOptType(0);
        return switchDTO;
    }

    /**
     * 获取批量改开关状态需要的参数
     * @param tenantUser
     * @param aggrSaveNavigationResourceReqs
     * @return
     */
    private static List<TenantResourceSwitchDTO> getTenantResourceSwitchDTOS(TenantUser tenantUser, AggrSaveNavigationResourceReq aggrSaveNavigationResourceReqs) {
        List<TenantResourceSwitchDTO> dtoList = aggrSaveNavigationResourceReqs.getCategorySwitchList().stream()
                .map(x -> {
                    TenantResourceSwitchDTO dto = new TenantResourceSwitchDTO();
                    dto.setTenantUserId(tenantUser.getTenantId()+"");
                    dto.setCategoryId(x.getCategoryId());
                    dto.setResourceSwitch(x.getResourceSwitch());
                    return dto;
                }).collect(Collectors.toList());
        return dtoList;
    }

    /**
     * 获取保存平台页面排序值需要的参数
     * @param tenantUser
     * @param aggrSaveNavigationResourceReqs
     * @return
     */
    private static NavigationResourceBO getNavigationResourceBO(TenantUser tenantUser, List<CategorySwitchVO> categorySwitchVOS, AggrSaveNavigationResourceReq aggrSaveNavigationResourceReqs) {
        NavigationResourceBO req = new NavigationResourceBO();
        // 获取 categorySwitchList 并转换为 ResourceConfig 列表
        List<NavigationResourceBO.ResourceConfig> tabConfigDetails = aggrSaveNavigationResourceReqs.getCategorySwitchList().stream()
                .map(x -> {
                    NavigationResourceBO.ResourceConfig config = new NavigationResourceBO.ResourceConfig();
                    config.setCategoryId(x.getCategoryId());
                    config.setOrderValue(String.valueOf(x.getOrderValue()));
                    config.setSourceType(x.getSourceType());
                    return config;
                })
                .collect(Collectors.toList());
        req.setTabConfigDetails(tabConfigDetails);
        req.setTenantId(String.valueOf(tenantUser.getTenantId()));
        req.setOwnerId(String.valueOf(tenantUser.getUserId()));
        req.setOwnerType(NAVIGATION_SOURCE_TYPE);
        req.setResourceType(ResourceTypeEnum.NAVIGATION.getCode());
        return req;
    }

    @Override
    public AggrDetailNavigationResourceResp aggrNavigationDetail(TenantUser tenantUser, AggrDetailNavigationResourceReq aggrDetailNavigationResourceReq) {
        // 将数据聚合到一起，然后转换成NavigationResourceReq
        List<NavigationResourceReq> customNavigationList = this.navigationDetail(tenantUser);

        TenantResourceSwitchDTO tenantResourceSwitchDTO = new TenantResourceSwitchDTO();
        tenantResourceSwitchDTO.setOptType(aggrDetailNavigationResourceReq.getOptType());
        tenantResourceSwitchDTO.setTenantUserId(String.valueOf(tenantUser.getTenantId()));
        List<CategorySwitchVO>  platformNavigationList = akuCunCmsClient.queryTenantResourceSwitchInfo(tenantResourceSwitchDTO);

        // 将数据聚合到一起，并转换成 List<AggrDetailNavigationResourceResp>
        Map<String, Integer> platformSortDataMap = new HashMap<>();
        List<NavigationResourceResp> customNavigationResps = new ArrayList<>();
        AggrDetailNavigationResourceResp response = new AggrDetailNavigationResourceResp();

        for(NavigationResourceReq customNavigation : customNavigationList){
            if(!Objects.equals(customNavigation.getSourceType(), PLAT_PAGE_SOURCE_TYPE)){
                NavigationResourceResp customNavigationResp = BeanCopyUtil.copy(customNavigation, NavigationResourceResp.class);
                customNavigationResps.add(customNavigationResp);
            }else{
                platformSortDataMap.put(customNavigation.getCategoryId(), customNavigation.getOrderValue());
            }
        }



        List<CategorySwitchResp>  platformNavigationResps = sortPlatformNavigationResps(tenantUser,platformNavigationList,customNavigationResps,platformSortDataMap);

        response.setNavigationResourceReqslist(customNavigationResps);
        response.setCategorySwitchList(platformNavigationResps);
        return response;
    }

    private static NavigationResourceBO getNavigationResourceBO(TenantUser tenantUser) {
        NavigationResourceBO req = new NavigationResourceBO();
        req.setTenantId(String.valueOf(tenantUser.getTenantId()));
        req.setOwnerId(String.valueOf(tenantUser.getUserId()));
        req.setOwnerType(NAVIGATION_SOURCE_TYPE);
        req.setResourceType(ResourceTypeEnum.NAVIGATION.getCode());
        return req;
    }

    private List<CategorySwitchResp> sortPlatformNavigationResps(TenantUser tenantUser,List<CategorySwitchVO> platformNavigationList,List<NavigationResourceResp> customNavigationResps,Map<String, Integer> platformSortDataMap){
        if(CollectionUtils.isEmpty(platformNavigationList)){
            return new ArrayList<>();
        }
        // 根据租户id查询平台页面的排序值
        //NavigationResourceBO req = getNavigationResourceBO(tenantUser);
        //NavigationResourceBO res = mshopCmsClient.getPlatPageDataByTenantInfo(req);
        //String jsonString = JSON.toJSONString(res.getTabConfigDetails());
        //List<Map<String, Object>> tabConfigDetailsList = JSON.parseObject(jsonString, new TypeReference<List<Map<String, Object>>>() {});

        List<CategorySwitchResp> platformNavigationResps = new ArrayList<>();
        int index = customNavigationResps.size();

        // 过滤掉平台数据中的顶部导航 不展示
        platformNavigationList = platformNavigationList.stream()
                .filter(Objects::nonNull)
                .filter(x -> !x.getCategoryId().equalsIgnoreCase("resource_0_all"))
                .collect(Collectors.toList());

        for(CategorySwitchVO platformNav : platformNavigationList){
            index++;
            CategorySwitchResp resp = new CategorySwitchResp();
            if(MapUtils.isEmpty(platformSortDataMap)){
                resp.setOrderValue(index+"");
            }else{
                Integer sortValue = platformSortDataMap.get(platformNav.getCategoryId());
                resp.setOrderValue(sortValue==null ? "999" : sortValue+"");
            }
            resp.setSourceType(PLAT_PAGE_SOURCE_TYPE);
            resp.setCategoryId(platformNav.getCategoryId());
            resp.setCategoryName(platformNav.getCategoryName());
            resp.setResourceSwitch(platformNav.getResourceSwitch());
            platformNavigationResps.add(resp);
        }




        return platformNavigationResps;
    }
}
