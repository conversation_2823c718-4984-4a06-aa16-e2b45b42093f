package com.mengxiang.mshop.cms.core.service.page;

import com.mengxiang.mshop.cms.core.model.result.PageTemplateResult;

import java.util.List;


/**
 * 页面组件服务
 * <AUTHOR>
 */
public interface TemplateService {


    /**
     * 模版列表
     * @param ownerId
     * @param ownerType
     * @return
     */
    List<PageTemplateResult> list(String ownerId, String ownerType,String pageUseType,Integer version);

    /**
     * 页面模版详情
     * @return
     */
    PageTemplateResult detail(String templateCode);
}
