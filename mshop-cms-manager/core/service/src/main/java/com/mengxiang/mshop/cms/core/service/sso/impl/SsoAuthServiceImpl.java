package com.mengxiang.mshop.cms.core.service.sso.impl;

import com.aikucun.security.sso.permission.dto.request.RoleQuery;
import com.aikucun.security.sso.permission.dto.request.UserQuery;
import com.aikucun.security.sso.permission.dto.response.Role;
import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.mshop.cms.common.service.integration.feign.sso.SsoClient;
import com.mengxiang.mshop.cms.core.service.sso.SsoAuthService;
import com.mengxiang.mshop.cms.core.service.sso.SsoConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查询认证信息
 * <AUTHOR>
 */
@Service
@Slf4j
public class SsoAuthServiceImpl implements SsoAuthService {
    @Autowired
    private SsoClient ssoClient;

    @Autowired
    private SsoConfig clientConfig;

    /**
     * sso user info query
     * @param authentication authentication
     * @return user info or null
     */
    @Override
    public UserInfo getUserInfo(String authentication) {
        if (StringUtils.isEmpty(authentication)) {
            throw new BusinessException("没有authentication");
        }

        UserQuery userQuery = new UserQuery();
        userQuery.setAuthentication(authentication);
        userQuery.setAppId(clientConfig.getClientId());
        userQuery.setAppSecret(clientConfig.getAppSecret());
        return ssoClient.getUserInfo(userQuery);
    }

    /**
     * 查询用户菜单功能
     * @param authentication authentication
     * @return user info or null
     */
    @Override
    public List<Role> getUserRoles(String authentication) {
        if (StringUtils.isEmpty(authentication)) {
            throw new BusinessException("没有authentication");
        }

        RoleQuery roleQuery = new RoleQuery();
        roleQuery.setAuthentication(authentication);
        roleQuery.setAppId(clientConfig.getClientId());
        roleQuery.setAppSecret(clientConfig.getAppSecret());
        return ssoClient.getUserRoles(roleQuery);
    }

    @Override
    public boolean hasAccessRoles(String authentication) {
        if(CollectionUtils.isEmpty(clientConfig.getAccessRoleCodes())) {
            return false;
        }

        List<Role> userRoles = getUserRoles(authentication);
        if(CollectionUtils.isEmpty(userRoles)) {
            return false;
        }

        //有任意一个角色，即有权限
        for (Role user : userRoles) {
            for (String roleCode : clientConfig.getAccessRoleCodes()) {
                if(user.getCode().equals(roleCode)) {
                    return true;
                }
            }
        }
        return false;
    }
}
