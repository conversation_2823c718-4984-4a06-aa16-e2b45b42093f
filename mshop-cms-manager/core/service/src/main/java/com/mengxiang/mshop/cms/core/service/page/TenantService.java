package com.mengxiang.mshop.cms.core.service.page;

import com.mengxiang.mshop.cms.service.facade.common.request.PointsPageConfigReq;
import com.mengxiang.mshop.cms.service.facade.common.request.PromotionCouponReq;
import com.mengxiang.mshop.cms.service.facade.common.request.TenantUser;
import com.mengxiang.mshop.cms.service.facade.common.result.PointsPageConfigResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.MerchantShopResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.PageMengXiangPrewLink;

import java.util.Map;


/**
 * 梦饷云租户页面组件服务
 */
public interface TenantService {

    String preview(Long tenantId,String pageCode, String version);

    Map<String, String> generatePageUrl(String pageCode,Long tenantId);


    PointsPageConfigResp searchPointsPageConfig(TenantUser tenantUser);

    void updatePointsPageConfig(TenantUser tenantUser,PointsPageConfigReq req);
}
