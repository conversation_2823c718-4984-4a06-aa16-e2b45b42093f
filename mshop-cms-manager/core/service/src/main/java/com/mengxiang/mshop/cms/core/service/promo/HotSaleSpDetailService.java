package com.mengxiang.mshop.cms.core.service.promo;


import com.akucun.common.Pagination;
import com.akucun.common.Result;
import com.mengxiang.mshop.cms.service.facade.common.result.HotSaleSpInfoThirdQueryDTO;
import com.mengxiang.mshop.cms.service.facade.common.result.HotSaleSpInfoThirdResVO;

public interface HotSaleSpDetailService {

    /**
     * 查询商家档期优惠券信息
     */
    Result<Pagination<HotSaleSpInfoThirdResVO>> querySpInfoFromMerchant(HotSaleSpInfoThirdQueryDTO dto);
    /**
     * 查询营销平台优惠券信息
     */
    Result<Pagination<HotSaleSpInfoThirdResVO>> querySpInfoFromSpPlat(HotSaleSpInfoThirdQueryDTO dto);
}
