package com.mengxiang.mshop.cms.core.service.page.mengxiang;

import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.MerchantShopResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.PageMengXiangPrewLink;

import java.util.Map;


/**
 * 梦饷页面组件服务
 */
public interface PageMengXiangService {

    /**
     * 预览
     */
    Map<String,PageMengXiangPrewLink> preview(String staffId, String pageCode,String version);
    
    Map<String,String>  generatePageUrl(String pageCode);

    MerchantShopResp getShopInfoByShopCode (String shopCode);

    JSONObject findShareText();

    String findShareTextByCategory(String category);

}
