package com.mengxiang.mshop.cms.core.service.promo.impl;

import com.akucun.mshop.common.util.BeanCopyUtil;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.merchant.platform.query.service.facade.common.result.QueryMerResult;
import com.mengxiang.merchant.platform.query.service.facade.common.result.QueryShopResult;
import com.mengxiang.mshop.cms.common.service.integration.feign.PromoActivityClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.merchant.MerchantClient;
import com.mengxiang.mshop.cms.core.service.promo.PromoActivityService;
import com.mengxiang.mshop.cms.service.facade.common.request.CouponListReq;
import com.mengxiang.mshop.cms.service.facade.common.request.PromotionActivityReq;
import com.mengxiang.mshop.cms.service.facade.common.request.PromotionCouponReq;
import com.mengxiang.mshop.cms.service.facade.common.result.PromotionActivityListResp;
import com.mengxiang.mshop.cms.service.facade.common.result.PromotionCouponResp;
import com.mengxiang.promo.service.facade.request.PromotionCouponActivityListRequest;
import com.mengxiang.promo.service.facade.request.PromotionCouponInfoRequest;
import com.mengxiang.promo.service.facade.request.PromotionCouponListRequest;
import com.mengxiang.promo.service.facade.request.coupon.CouponQueryByAwdIdsReq;
import com.mengxiang.promo.service.facade.response.PromotionCouponActivityListResp;
import com.mengxiang.promo.service.facade.response.PromotionCouponInfoResp;
import com.mengxiang.promo.service.facade.response.PromotionCouponListResp;
import com.mengxiang.promo.service.facade.response.coupon.CouponAwdReceiveVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mengxiang.base.common.model.result.Pagination;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PromoActivityServiceImpl implements PromoActivityService{

    @Autowired
    private PromoActivityClient promoActivityClient;
    @Autowired
    private MerchantClient merchantClient;


    /**
     * 查询营销活动列表
     * @param promotionCouponReq
     * @return
     */
    @Override
    public Pagination<PromotionActivityListResp> searchPromoActivityList(PromotionActivityReq promotionCouponReq){
        if(StringUtils.isBlank(promotionCouponReq.getPromoActivityVersion())){
            throw new BusinessException("营销活动版本不能为空");
        }
        if(StringUtils.isBlank(promotionCouponReq.getPromoActivityOwner())){
            throw new BusinessException("营销活动所属方不能为空");
        }

        PromotionCouponActivityListRequest request = new PromotionCouponActivityListRequest();
        request.setPromoActivityName(promotionCouponReq.getPromoActivityName());
        request.setPromoActivityOwner(promotionCouponReq.getPromoActivityOwner());
        request.setPromoActivityVersion(promotionCouponReq.getPromoActivityVersion());
        request.setActivityStatus(promotionCouponReq.getActivityStatus());
        request.setPromoActivityId(promotionCouponReq.getPromoActivityId());
        request.setPromoActivityStartTimeStart(promotionCouponReq.getPromoActivityStartTimeStart());
        request.setPromoActivityStartTimeEnd(promotionCouponReq.getPromoActivityStartTimeEnd());
        request.setPromoActivityEndTimeStart(promotionCouponReq.getPromoActivityEndTimeStart());
        request.setPromoActivityEndTimeEnd(promotionCouponReq.getPromoActivityEndTimeEnd());
        request.setPromoActivityOwnerId(promotionCouponReq.getPromoActivityOwnerId());
        request.setPage(promotionCouponReq.getPageIndex());
        request.setSize(promotionCouponReq.getPageSize());
        request.setPointsCouponActivity(promotionCouponReq.getPointsCoupon());
        Pagination<PromotionCouponActivityListResp> activityListRespPagination = promoActivityClient.promoActivityList(request);
        Pagination<PromotionActivityListResp> result = new Pagination();
        if(Objects.nonNull(activityListRespPagination) && CollectionUtils.isNotEmpty(activityListRespPagination.getResult())){
            //循环查询优惠券
            List<PromotionActivityListResp> list = Lists.newArrayList();
            for(PromotionCouponActivityListResp resp:activityListRespPagination.getResult()){
                PromotionActivityListResp newResp = BeanCopyUtil.copy(resp,PromotionActivityListResp.class);
                //查询优惠券列表
                PromotionCouponReq couponReq = new PromotionCouponReq();
                couponReq.setPromoActivityId(resp.getPromoActivityId());
                couponReq.setPromoActivityOwner(resp.getPromoActivityOwner());
                couponReq.setPromoActivityVersion(promotionCouponReq.getPromoActivityVersion());
                couponReq.setPointsCoupon(promotionCouponReq.getPointsCoupon());
                List<PromotionCouponListResp> couponListRespList = searchPromoCouponList(couponReq);
                newResp.setCouponList(couponListRespList);
                list.add(newResp);
            }
            result.setResult(list);
            result.setPageIndex(activityListRespPagination.getPageIndex());
            result.setCurrentPageSize(activityListRespPagination.getCurrentPageSize());
            result.setPages(activityListRespPagination.getPages());
            result.setPageSize(activityListRespPagination.getPageSize());
            result.setTotal(activityListRespPagination.getTotal());
        }

        return result;
    }

    /**
     * 查询优惠券列表
     * @param promotionCouponReq
     * @return
     */
    @Override
    public List<PromotionCouponListResp> searchPromoCouponList(PromotionCouponReq promotionCouponReq){
        if(StringUtils.isBlank(promotionCouponReq.getPromoActivityVersion())){
            throw new BusinessException("营销活动版本不能为空");
        }
        if(StringUtils.isBlank(promotionCouponReq.getPromoActivityOwner())){
            throw new BusinessException("营销活动所属方不能为空");
        }
        if(StringUtils.isBlank(promotionCouponReq.getPromoActivityId())){
            throw new BusinessException("营销活动ID不能为空");
        }

        PromotionCouponListRequest request = new PromotionCouponListRequest();
        request.setPromoActivityOwner(promotionCouponReq.getPromoActivityOwner());
        request.setPromoActivityVersion(promotionCouponReq.getPromoActivityVersion());
        request.setPromoActivityId(promotionCouponReq.getPromoActivityId());
        request.setPointsCoupon(promotionCouponReq.getPointsCoupon());
//        request.setPage(promotionCouponReq.getPageIndex());
//        request.setSize(promotionCouponReq.getPageSize());

        return  promoActivityClient.promoCouponList(request);
    }

    /**
     * 查询优惠券信息
     * @param list
     * @return
     */
    @Override
    public List<PromotionCouponResp> promoCouponListByCouponId(List<CouponListReq> list,Boolean pointsCoupon){
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        Map<String,String> map = list.stream().collect(Collectors.toMap(CouponListReq::getCouponId,CouponListReq::getPromoActivityId,(e1,e2)->e1));

        List<PromotionCouponResp> result = Lists.newArrayList();
        List<PromotionCouponInfoRequest> couponIdList = Lists.newArrayList();
        for(CouponListReq req:list){
            PromotionCouponInfoRequest request = new PromotionCouponInfoRequest();
            request.setCouponId(req.getCouponId());
            request.setPromoActivityId(req.getPromoActivityId());
            couponIdList.add(request);
        }
        List<PromotionCouponInfoResp>  couponAwdReceiveVOList = promoActivityClient.promoCouponListById(couponIdList,pointsCoupon);
        //根据商家店铺code查询商家信息
       List<String> merchantShopCodeList = couponAwdReceiveVOList.stream().filter(x->1==x.getCouponVersionType()).map(PromotionCouponInfoResp::getMerchantShopCode).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String,QueryShopResult> shopMap = merchantClient.getShopInfoListByShopCodeList(merchantShopCodeList);
        for(PromotionCouponInfoResp vo :couponAwdReceiveVOList){
            PromotionCouponResp resp = BeanCopyUtil.copy(vo,PromotionCouponResp.class);
            if(StringUtils.isNotBlank(vo.getCouponTemplateName())){
                resp.setCoupon(vo.getCouponTemplateName());
            }else if(Objects.nonNull(vo.getThresholdAmount()) && Objects.nonNull(vo.getAmount())){
                resp.setCoupon("满"+vo.getThresholdAmount().stripTrailingZeros().toPlainString()+"减"+vo.getAmount().stripTrailingZeros().toPlainString());
            }
            resp.setPromoActivityId(map.get(vo.getCouponId()));
            resp.setPromoActivityName(vo.getPromoActivityName());
            if(0 == vo.getCouponVersionType() && "MERCHANT".equals(vo.getCouponScope()) && StringUtils.isNotBlank(vo.getMerchantCompanyName())){
                //老券 商家券
                resp.setMerName(vo.getMerchantCompanyName());
            }else if(1 == vo.getCouponVersionType() && StringUtils.isNotBlank(vo.getMerchantShopCode())){
                //新券商家券
                if(MapUtils.isNotEmpty(shopMap)){
                    QueryShopResult shop = shopMap.get(vo.getMerchantShopCode());
                    if(Objects.nonNull(shop)){
                        resp.setMerName(shop.getMerName());
                    }
                }

            }

            result.add(resp);
        }
        return result;
    }

    /**
     * 优惠券选择校验
     * @param promoActivityId
     * @param promoActivityRange
     * @return
     */
    @Override
    public Boolean promoCouponCheck(String promoActivityId,String promoActivityRange){
        return promoActivityClient.promoCouponCheck(promoActivityId,promoActivityRange);
    }


}