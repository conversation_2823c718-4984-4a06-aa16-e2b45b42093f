package com.mengxiang.mshop.cms.core.service.page.impl;


import com.aikucun.dc.aiward.facade.stub.common.OrderDto;
import com.aikucun.dc.aiward.facade.stub.rule.enumfile.SourceCode;
import com.aikucun.dc.aiward.facade.stub.rule.sell.SellRuleCreateDto;
import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.akucun.merchant.platform.model.vo.shop.MerShopVO;
import com.akucun.meroperationtool.model.dto.AmbUserDTO;
import com.akucun.meroperationtool.model.dto.AmbUserSimpleDTO;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.base.share.service.facade.common.enums.CarrierEnum;
import com.mengxiang.base.share.service.facade.common.enums.ShareTypeEnum;
import com.mengxiang.base.share.service.facade.common.enums.TargetChannelEnum;
import com.mengxiang.base.share.service.facade.common.enums.UserTypeEnum;
import com.mengxiang.base.share.service.facade.common.request.GenerateShortCodeLinkRequest;
import com.mengxiang.base.share.service.facade.common.result.GenerateShortCodeLinkVO;
import com.mengxiang.merchant.platform.query.service.facade.common.result.QueryShopResult;
import com.mengxiang.mshop.cms.common.service.integration.feign.BIDataServiceClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.PageClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.merchant.MerchantClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.share.ShortCodeServiceClient;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.domain.base.TimeConfigBO;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.model.enums.TemplateUseChannel;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.SellRuleGetBatchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.service.page.PageQueryService;
import com.mengxiang.mshop.cms.core.service.page.PageService;
import com.mengxiang.mshop.cms.service.facade.common.request.PageSearchReq;
import com.mengxiang.mshop.cms.service.facade.common.result.PageSelectResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.PageMengXiangPrewLink;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 页面组件服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PageServiceImpl implements PageService {

    @Resource
    private PageClient pageClient;
    @Autowired
    private BIDataServiceClient biDataServiceClient;

    @Value("${page.merchant.default.create.components:}")
    private String defaultComponents;
    @Value("${page.merchant.default.create.templateCode:2}")
    private String defaultTemplateCode;

    @Value("${page.merchant.default.create.components.v2:}")
    private String defaultComponentsV2;
    @Value("${page.merchant.default.create.templateCode.V2:4}")
    private String defaultTemplateCodeV2;

    @Value("${page.merchant.default.create.name:默认页面}")
    private String defaultName;
    @Value("${page.merchant.default.create.orderBy:688}")
    private String defaultOrderBy;
    @Value("${page.tenantId:151738493257170900}")
    private String tenantId;
    @Autowired
    private PageQueryService pageQueryService;

    @Autowired
    private MerchantClient merchantClient;

    @Value("${mengxiang.micro.app.url:http://h5shop-stable.akucun.com/mx-shop-micro?customPageCode=}")
    private String mengXiangMicroUrlByApp;

    @Value("${mengxiang.micro.h5.url:http://h5shop-stable.akucun.com/mx-shop-micro?customPageCode=}")
    private String mengXiangMicroUrlByH5;

    @Value("${mengxiang.micro.mini.url:http://fe-static.akcstable.com/mx-shop-micro?customPageCode=}")
    private String mengXiangMicroUrlByMini;


    @Value("${mengxiang.default.shopId:1}")
    private Long shopId;

    @Autowired
    private ShortCodeServiceClient shortCodeServiceClient;
    /**
     * 页面保存/发布
     *
     * @param savePageRequest
     * @return
     */
    @Override
    public Result<PageBO> save(SavePageRequest savePageRequest) {
        return pageClient.save(savePageRequest);
    }

    @Override
    public Result<PageBO> saveByMengXiang(SavePageRequest req, UserInfo userInfo) {
        //判断页面是不是商家创建的
        if (StringUtils.isNotBlank(req.getPageCode())
                && Objects.equals(req.getOwnerType(),PageOwnerType.SUPPLIER.getOwnerType())
                && Objects.equals(req.getType(),PageType.MARKET_PAGE.getType())) {
            //查询审批人列表
            buildAuditList(req, req.getOwnerId());

        }else{
            req.setOwnerId(userInfo.getUsercode());
            req.setOwnerType(PageOwnerType.MENGXIANG.getOwnerType());
        }
        final String separator = ",";
        String channel = StringUtils.join(new String[]{
                TemplateUseChannel.APP.getChannel(),
                TemplateUseChannel.H5.getChannel(),
                TemplateUseChannel.MINIAPP.getChannel()
        }, separator);
        req.setChannel(channel);
        req.setCreateBy(userInfo.getRealname());
        req.setCreateUserId(userInfo.getUsercode());
        req.setUpdateBy(userInfo.getUsercode());

        req.setManagerNoDirect(userInfo.getManagerNoDirectNew());
        req.setCreateUserCode(userInfo.getUsercode());

        //商家创建需要查询审批人
        return pageClient.save(req);
    }


    /**
     * 页面保存/发布（商家）
     *
     * @param savePageRequest
     * @return
     */
    @Override
    public Result<PageBO> saveByMer(SavePageRequest savePageRequest, MerchantUserInfo userInfo) {
        if (savePageRequest.getType().equals(PageType.MARKET_PAGE.getType())) {
            //查询审批人列表
            buildAuditList(savePageRequest, userInfo.getShopCode());
        }
        savePageRequest.setMerchantCode(userInfo.getMerchantCode());
        return pageClient.save(savePageRequest);
    }

    @Override
    public Result<List<PageRuleInfoResult>> getRuleBatch(SellRuleGetBatchRequest request) {
        return pageClient.getRuleBatch(request);
    }

    @Override
    public Result<Boolean> setPageInvalidation(String pageCode, String version, String updateBy, String updateUserId) {
        return pageClient.setPageInvalidation(pageCode, version, updateBy, updateUserId);
    }

    /**
     * 页面信息
     *
     * @param pageCode
     * @param version
     * @return
     */
    @Override
    public Result<PageBO> detail(String pageCode, String version) {
        return pageClient.detail(pageCode, version);
    }

    /**
     * 使用模版
     *
     * @param templateCode
     * @return
     */
    @Override
    public Result<PageBO> detailByTemplate(String templateCode, String ownerId, String ownerType) {
        return pageClient.detailByTemplate(templateCode, ownerId, ownerType);
    }


    /**
     * 恢复至上一发布版本
     *
     * @param pageCode
     * @return
     */
    @Override
    public Result<PageBO> detailToBeforePublished(String pageCode) {
        return pageClient.detailToBeforePublished(pageCode);
    }

    /**
     * 预览
     *
     * @param ownerType
     * @param pageCode
     * @param role
     * @return
     */
    @Override
    public Result<String> preview(String ownerType, String pageCode, String role) {
        return pageClient.preview(ownerType, pageCode, role);
    }

    /**
     * 预览
     *
     * @param ownerType
     * @param pageCode
     * @param role
     * @return
     */
    @Override
    public Map<String, PageMengXiangPrewLink> previewMap(String ownerType, String pageCode, String role) {
        Map<String, PageMengXiangPrewLink> map = Maps.newHashMap();
        Result<String> result = pageClient.preview(ownerType, pageCode, role);
        if (Objects.nonNull(result) && result.isSuccess() && StringUtils.isNotBlank(result.getData())) {
            PageMengXiangPrewLink pageMengXiangPrewLink = new PageMengXiangPrewLink();
            pageMengXiangPrewLink.setMerShopPreviewLink(result.getData());
            map.put("APP", pageMengXiangPrewLink);
        }
        return map;
    }

    @Override
    public Map<String, PageMengXiangPrewLink> previewShop(String staffId, String pageCode, String version,String merShopCode) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(pageCode), "页面code 不能为空");
        Preconditions.checkArgument(StringUtils.isNotEmpty(version), "页面版本 不能为空");
        Map<String, PageMengXiangPrewLink> resp = new HashedMap();
        PageMengXiangPrewLink minilink = new PageMengXiangPrewLink();
        //店主
        String rMiniPreviewLink = getShopPreviewLink(null, TargetChannelEnum.MINI, UserTypeEnum.SELLER, pageCode, version,merShopCode);
        minilink.setRShopPreviewLink(rMiniPreviewLink);
        //店长
        minilink.setEShopPreviewLink(rMiniPreviewLink);
        //c
        String cMiniPreviewLink = getShopPreviewLink(shopId, TargetChannelEnum.MINI, UserTypeEnum.USER, pageCode, version,merShopCode);
        minilink.setCShopPreviewLink(cMiniPreviewLink);
        resp.put("MINI", minilink);

        PageMengXiangPrewLink h5link = new PageMengXiangPrewLink();
        //店主
        String rH5link = getShopPreviewLink(null, TargetChannelEnum.H5, UserTypeEnum.SELLER, pageCode, version,merShopCode);
        h5link.setRShopPreviewLink(rH5link);
        h5link.setEShopPreviewLink(rH5link);
        //c
        String cH5link = getShopPreviewLink(shopId, TargetChannelEnum.H5, UserTypeEnum.USER, pageCode, version,merShopCode);
        h5link.setCShopPreviewLink(cH5link);
        resp.put("H5", h5link);

        PageMengXiangPrewLink appLink = new PageMengXiangPrewLink();
//        String url = new StringBuilder().append(mengXiangMicroUrlByApp).append(pageCode).append("&version=").append(version)
//                .append("&preview=true").toString();
        Result<String>  appUrl =  pageClient.preview(PageOwnerType.SUPPLIER.getOwnerType(),pageCode,"r");
        appLink.setCShopPreviewLink(null);
        appLink.setRShopPreviewLink(appUrl.getData());
        appLink.setEShopPreviewLink(null);
        resp.put("APP", appLink);
        return resp;
    }


    private String getShopPreviewLink(Long shopId, TargetChannelEnum channel, UserTypeEnum userType, String pageCode,
                                  String version,String merShopCode) {
        GenerateShortCodeLinkRequest req = new GenerateShortCodeLinkRequest();
        req.setTargetChannel(channel.getCode());
        req.setCarrier(CarrierEnum.POSTER.getCode());
        req.setContentType("STORE");
        req.setContentId(merShopCode);
        //页面参数
        Map<String, String> shortCodeExtra = Maps.newHashMap();
        shortCodeExtra.put("preview", "true");
        shortCodeExtra.put("customPageCode", pageCode);
        shortCodeExtra.put("version", version);
        req.setShortCodeExtra(shortCodeExtra);

        com.mengxiang.base.share.service.facade.common.request.UserInfo userInfo = new com.mengxiang.base.share.service.facade.common.request.UserInfo();
        userInfo.setShopId(shopId);
        userInfo.setUserType(userType.name());
        req.setUserInfo(userInfo);
        GenerateShortCodeLinkVO link = shortCodeServiceClient.generateShortCodeLink(req);
        if (null != link) {
            return link.getUrl();
        }
        return "";
    }


    /**
     * 复制并创建新页面
     *
     * @param pageCode
     * @param version
     * @return
     */
    @Override
    public Result<PageBO> detailToNewPage(String pageCode, String version) {
        return pageClient.detailToNewPage(pageCode, version);
    }

    @Override
    public Result<Pagination<PageSelectResp>> pageSelect(PageSearchReq req) {
        PageSearchRequest request = this.buildPageSearchParam(req);
        Long total = 0L;
        List<PageSelectResp> respList = new ArrayList<>();
        Result<Pagination<PageSelectResult>> paginationResult = pageClient.pageSelect(request);
        if (null != paginationResult && paginationResult.isSuccess() && null != paginationResult.getData() && CollectionUtils.isNotEmpty(paginationResult.getData().getResult())) {
            List<PageSelectResult> result = paginationResult.getData().getResult();
            total = paginationResult.getData().getTotal();
            result.forEach(a -> {
                PageSelectResp resp = this.convertPageResp(a);
                respList.add(resp);
            });
        } else {
            log.warn("[pageFeign.pageSelect]查询页面列表req:{},result:{}", JSON.toJSONString(req), JSON.toJSONString(paginationResult));
            return Result.error(paginationResult.getCode(), paginationResult.getMessage());
        }

        Pagination pagination = new Pagination(req.getPageIndex(), req.getPageSize(), total, respList);
        return Result.success(pagination);
    }

    /**
     * 页面对象转换
     *
     * @param a
     * @return
     */
    private PageSelectResp convertPageResp(PageSelectResult a) {
        PageSelectResp resp = new PageSelectResp();
        resp.setPageCode(a.getPageCode());
        resp.setPublishFlag(a.getPublishFlag());
        resp.setName(a.getName());
        resp.setCreateTime(DateUtil.dateToStrLong(a.getCreateTime()));
        resp.setUpdateTime(DateUtil.dateToStrLong(a.getUpdateTime()));
        resp.setStatus(a.getStatus());
        resp.setSubTitle(a.getSubTitle());
        resp.setTitle(a.getTitle());
        resp.setBizType(a.getBizType());
        resp.setVersion(a.getVersion());
        resp.setUpdateBy(a.getUpdateBy());
        resp.setCreateBy(a.getCreateBy());
        resp.setPageType(a.getPageType());
        return resp;
    }

    private PageSearchRequest buildPageSearchParam(PageSearchReq req) {
        PageSearchRequest request = new PageSearchRequest();
        request.setCreateBy(req.getCreateBy());
        request.setPageSize(req.getPageSize());
        request.setPageIndex(req.getPageIndex());
        request.setPageCodeList(req.getPageCodeList());
        request.setChannel(req.getChannel());
        request.setCreateTimeEnd(req.getCreateTimeEnd());
        request.setCreateTimeStart(req.getCreateTimeStart());
        request.setUpdateTimeEnd(req.getUpdateTimeEnd());
        request.setUpdateTimeStart(req.getUpdateTimeStart());
        request.setName(req.getName());
        request.setOwnerType(req.getOwnerType());
        request.setStatus(req.getStatus());
        request.setSubTitle(req.getSubTitle());
        request.setTitle(req.getTitle());
        request.setTenantId(req.getTenantId());
        request.setVersion(req.getVersion());

        return request;
    }


    /**
     * 创建默认页面
     */
    @Override
    public Result<PageBO> createDefaultPage(String shopCode) {
        QueryShopResult queryShopInfo = merchantClient.getShopInfoByShopCode(shopCode);
        if (Objects.isNull(queryShopInfo)) {
            return null;
        }
        return createDefaultPage(queryShopInfo.getShopCode(), queryShopInfo.getMerCode(), queryShopInfo.getShopName());
    }


    /**
     * 创建默认页面
     */
    @Override
    public Result<PageBO> createDefaultPage(String shopCode, String mCode, String operatorUserName) {
        try {
            //查询商家是否有装修
            PageSearchReq pageSearchReq = new PageSearchReq();
            pageSearchReq.setChannel(TemplateUseChannel.APP.getChannel());
            pageSearchReq.setTenantId(tenantId);
            pageSearchReq.setOwnerId(shopCode);
            pageSearchReq.setPageIndex(1);
            pageSearchReq.setPageSize(1);
            pageSearchReq.setOwnerType(PageOwnerType.SUPPLIER.getOwnerType());
            pageSearchReq.setType(PageType.SHOP_PAGE.getType());
            Result<Pagination<PageSelectResp>> paginationResult = pageQueryService.pageSelect(pageSearchReq);
            if (Objects.nonNull(paginationResult)
                    && paginationResult.isSuccess()
                    && Objects.nonNull(paginationResult.getData())
                    && CollectionUtils.isNotEmpty(paginationResult.getData().getResult())) {
                //有创建页面 不创建默认页面
                log.warn("createDefaultPage paginationResult  req:{},{},{}", shopCode, mCode, operatorUserName);
                return Result.error("createDefaultPage paginationResult ");
            }
            //创建模板1的页面
            createPageByTemplate(shopCode);
        } catch (Exception ex) {
            log.error("createDefaultPage createRule is null req:{},{},{}", shopCode, mCode, operatorUserName, ex);
        }
        return Result.success(null);
    }

    /**
     * 创建模板1的页面
     * @param shopCode
     */
    private void createPageByTemplate(String shopCode){
        try {
            SavePageRequest req = new SavePageRequest();
            req.setTenantId(tenantId);
            req.setChannel(TemplateUseChannel.APP.getChannel());
            req.setCreateBy("system");
            req.setCreateUserId("system");
            req.setUpdateBy("system");
            req.setOwnerId(shopCode);
            req.setOwnerType(PageOwnerType.SUPPLIER.getOwnerType());
            req.setComponents(defaultComponentsV2);
            req.setName(defaultName);
            req.setSearchBox(1);
            req.setSearchFlag(1);
            req.setTemplateCode(defaultTemplateCodeV2);
            req.setOperateType(2);
            TimeConfigBO timeConfig = new TimeConfigBO();
            timeConfig.setEffectiveType(1);
            req.setTimeConfig(timeConfig);
            req.setType("SHOP");
            Result<PageBO> result = save(req);
            log.info("createPageByTemplate result:{} ", JSON.toJSONString(result));
        } catch (Exception ex) {
            log.error("createPageByTemplate error req:{}", shopCode, ex);
        }
    }

    /**
     * 创建默认页面
     */
    @Override
    public Result<PageBO> updateMerPage(String shopCode) {
        try {
            QueryShopResult queryShopInfo = merchantClient.getShopInfoByShopCode(shopCode);
            if (Objects.isNull(queryShopInfo)) {
                return null;
            }
            //查询商家是否有生效的页面
            PageBO pageBO = pageClient.detailByType(shopCode,PageType.SHOP_PAGE.getType());
            if(Objects.nonNull(pageBO)){
                //失效页面
                pageClient.setPageInvalidation(pageBO.getPageCode(), pageBO.getVersion(),"system","system");
            }
            //创建模板1的页面
            createPageByTemplate(shopCode);
        } catch (Exception ex) {
            log.error("updateMerPage error  req:{}", shopCode, ex);
        }
        return Result.success(null);
    }


    /**
     * 创建商家规则
     *
     * @param merchantId
     * @param merchantShopId
     * @param operatorUserName
     * @return
     */
    private Long createRule(String merchantId, String merchantShopId, String operatorUserName) {
        try {
            SellRuleCreateDto req = new SellRuleCreateDto();
            req.setName("商家规则");
            req.setSourceCode(SourceCode.MERCHANT);
            req.setRuleType(1);
            req.setRuleBindType(5);
            req.setEnable(Boolean.FALSE);
            req.setMerchantId(merchantId);
            req.setMerchantShopId(merchantShopId);
            req.setOperatorUserName(operatorUserName);
            req.setFilter(new String[0]);
            OrderDto orderDto = new OrderDto();
            orderDto.setOrderBy(defaultOrderBy);
            orderDto.setOrderType("DESC");
            req.setOrderList(Lists.newArrayList(orderDto));
            return biDataServiceClient.createRuleByMerchantInfo(req);
        } catch (Exception ex) {
            log.error("createRule error req:{},{},{}", merchantId, merchantShopId, operatorUserName, ex);
        }
        return null;
    }


    /**
     * 查询端上的的生效页面
     *
     * @param pageCode
     * @return
     */
    @Override
    public Result<PageBO> pageDetailByCode(String pageCode) {
        return pageClient.detail(pageCode);
    }

    /**
     * 设置当前页面为主页
     *
     * @param pageCode
     * @param ownerId
     * @param ownerType
     * @return
     */
    @Override
    public Result<Void> setPageIndex(String pageCode, String ownerId, String pageType, String ownerType) {
        return pageClient.setPageIndex(pageCode, ownerId, pageType, ownerType);
    }

    private void buildAuditList(SavePageRequest savePageRequest, String shopCode) {
        //商家会场3.0需要查询审批人
        List<String> auditList = Lists.newArrayList();
        List<String> categoryLeaderList = Lists.newArrayList();
        //根据店铺code查询BD
        MerShopVO merShopVO = merchantClient.queryByShopCode(shopCode);
        if (Objects.nonNull(merShopVO)) {
            if (StringUtils.isNotBlank(merShopVO.getBdUserCode())) {
                auditList.add(merShopVO.getBdUserCode());
            } else if (StringUtils.isNotBlank(merShopVO.getBdCode())) {
                //没有工号有gpmID 在查一次换工号
                List<AmbUserDTO> ambUserDTOList = merchantClient.listAmbUser(Lists.newArrayList(merShopVO.getBdCode()), true);
                if (CollectionUtils.isNotEmpty(ambUserDTOList)
                        && Objects.nonNull(ambUserDTOList.get(0))
                        && StringUtils.isNotBlank(ambUserDTOList.get(0).getUserCode())) {
                    auditList.add(ambUserDTOList.get(0).getUserCode());
                }
            }
            //查询类目长通知用
            if (StringUtils.isNotBlank(merShopVO.getBdBusinessTypes()) && StringUtils.isNotBlank(merShopVO.getCategoryCode())) {
                //查询不到BD在查询类目长
                List<AmbUserSimpleDTO> ambUserDTOList = merchantClient.listCategoryLeader(merShopVO.getCategoryCode(), Integer.parseInt(merShopVO.getBdBusinessTypes()), null);
                if (CollectionUtils.isNotEmpty(ambUserDTOList)) {
                    categoryLeaderList = ambUserDTOList.stream().filter(Objects::nonNull).filter(x -> StringUtils.isNotBlank(x.getUserCode())).map(x -> x.getUserCode()).collect(Collectors.toList());
                    savePageRequest.setCategoryLeaderList(categoryLeaderList);
                }
            }
            if (auditList.size() == 0 && CollectionUtils.isNotEmpty(categoryLeaderList)) {
                //查询不到BD在设置类目长审批
                auditList.addAll(categoryLeaderList);
            }

        }
        //查询管理员角色
//        List<AmbUserSimpleDTO> ambUserDTOList = merchantClient.listAdmin();
//        if (CollectionUtils.isNotEmpty(ambUserDTOList)) {
//            List<String> userCodeList = ambUserDTOList.stream().filter(Objects::nonNull).filter(x -> StringUtils.isNotBlank(x.getUserCode())).map(x -> x.getUserCode()).collect(Collectors.toList());
//            auditList.addAll(userCodeList);
//        }
        savePageRequest.setAuditList(auditList);
    }

}

