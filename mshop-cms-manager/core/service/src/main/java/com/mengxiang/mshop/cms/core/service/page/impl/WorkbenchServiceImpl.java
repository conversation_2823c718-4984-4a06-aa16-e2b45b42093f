package com.mengxiang.mshop.cms.core.service.page.impl;


import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.service.integration.feign.WorkbenchClient;
import com.mengxiang.mshop.cms.core.model.request.workflow.WorkflowInfoRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.WorkflowInfoResp;
import com.mengxiang.mshop.cms.core.service.page.WorkbenchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 工作流服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkbenchServiceImpl implements WorkbenchService {

    @Resource
    private WorkbenchClient workbenchClient;


    @Override
    public Result<WorkflowInfoResp> findWorkflowInfo(WorkflowInfoRequest req) {
        return workbenchClient.findWorkflowInfo(req);
    }
}
