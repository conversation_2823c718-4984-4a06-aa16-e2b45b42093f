package com.mengxiang.mshop.cms.core.service.sso;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@Data
public class SsoConfig {
    
    @Value("${sso.client.app-id:}")
    private String clientId;
    
    @Value("${sso.client.secret:}")
    private String appSecret;
    
    /**
     * 允许访问的角色code
     */
    @Value("${sso.client.accessRoleCodes:R38344305}")
    private List<String> accessRoleCodes;
}
