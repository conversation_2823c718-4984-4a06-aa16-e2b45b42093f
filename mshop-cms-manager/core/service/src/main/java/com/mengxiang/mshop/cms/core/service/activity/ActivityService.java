package com.mengxiang.mshop.cms.core.service.activity;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.mshop.aggr.core.model.vo.activity.ActivityListVO;
import com.mengxiang.mshop.cms.service.facade.common.request.ActivityReq;

/**
 * <AUTHOR>
 */
public interface ActivityService {

    /**
     * 查询活动列表
     * @param activityReq
     * @return
     */
    Pagination<ActivityListVO> activityList(ActivityReq activityReq);
}
