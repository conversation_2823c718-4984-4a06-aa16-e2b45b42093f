package com.mengxiang.mshop.cms.core.service.page.impl;

import cn.hutool.core.date.DateUnit;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.request.OperationLogRequest;
import com.mengxiang.mshop.cms.core.model.request.OperationLogSaveRequest;
import com.mengxiang.mshop.cms.core.model.result.OperationLogResult;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.service.page.OperationLogService;
import com.mengxiang.mshop.cms.service.facade.common.feign.operationLog.OperationLogFeign;
import com.mengxiang.mshop.cms.service.facade.common.request.OperationLogReq;
import com.mengxiang.mshop.cms.service.facade.common.request.OperationLogSaveReq;
import com.mengxiang.mshop.cms.service.facade.common.result.OperationLogResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 操作日志实现类
 */
@Service
@Slf4j
public class OperationLogServiceImpl implements OperationLogService {

    @Resource
    private OperationLogFeign operationLogFeign;


    
    @Override
    public Result<Pagination<OperationLogResp>> operationLogPage(OperationLogReq req) {

        //校验
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getBizType()), "业务类型不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getBizCode()), "业务编号不能为空");

        //查询装修核心服务
        OperationLogRequest request = new OperationLogRequest();
        request.setBizCode(req.getBizCode());
        request.setPageIndex(req.getPageIndex());
        request.setPageSize(req.getPageSize());
        request.setBizType(req.getBizType());
        request.setAction(req.getAction());
        Long total = 0L;
        List<OperationLogResp> respList = new ArrayList<>();

        try {
            Result<Pagination<OperationLogResult>> paginationResult = operationLogFeign.operationLogPage(request);
            log.info("[operationLogFeign.operationLogPage]查询操作分页数据req:{},result:{}", JSON.toJSONString(req), JSON.toJSON(paginationResult));

            if (null != paginationResult && null != paginationResult.getData()) {
                List<OperationLogResult> result = paginationResult.getData().getResult();
                total = paginationResult.getData().getTotal();
                result.forEach(a -> {
                    OperationLogResp resp = this.convertOperationLog(a);
                    respList.add(resp);
                });
            }
        } catch (Exception e) {
            log.warn("[operationLogFeign.operationLogPage]发生异常", e);
            throw new BusinessException("分页查询操作日志发生异常");
        }

        Pagination pagination = new Pagination(req.getPageIndex(),req.getPageSize(), total, respList);
        return Result.success(pagination);
    }

    /**
     * 操作日志对象转换
     * @param a
     * @return
     */
    private OperationLogResp convertOperationLog(OperationLogResult a) {
        OperationLogResp resp = new OperationLogResp();
        resp.setAction(a.getAction());
        resp.setBizType(a.getBizType());
        resp.setRemark(a.getRemark());
        resp.setBizCode(a.getBizCode());
        resp.setAfterData(a.getAfterData());
        resp.setBeforeData(a.getBeforeData());
        resp.setCreateBy(a.getCreateBy());
        resp.setCreateTime(DateUtil.dateToStrLong(a.getCreateTime()));
        resp.setOwnerType(a.getOwnerType());
        return resp;
    }

    @Override
    public Result<Boolean> saveOperationLog(OperationLogSaveReq req) {
        //校验
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getBizType()), "业务类型不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getBizCode()), "业务编号不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getAction()), "业务行为不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getCreateBy()), "操作人不能为空");

        //调用装修核心服务
        OperationLogSaveRequest request = new OperationLogSaveRequest();
        request.setAction(req.getAction());
        request.setAfterData(req.getAfterData());
        request.setBizCode(req.getBizCode());
        request.setBizType(req.getBizType());
        request.setRemark(req.getRemark());
        request.setBeforeData(req.getBeforeData());
        request.setCreateBy(req.getCreateBy());
        request.setCreateUserId(req.getCreateUserId());
        request.setOwnerType(req.getOwnerType());
        boolean success = false;
        try {
            Result<Boolean> result = operationLogFeign.saveOperationLog(request);
            if (null != result || null != result.getData()) {
                success = result.getData();
            } else {
                log.warn("[operationLogFeign.saveOperationLog]保存操作日志失败req:{},result:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.warn("[operationLogFeign.saveOperationLog]发生异常", e);
            throw new BusinessException("保存操作记录异常");
        }
        return Result.success(success);
    }
}
