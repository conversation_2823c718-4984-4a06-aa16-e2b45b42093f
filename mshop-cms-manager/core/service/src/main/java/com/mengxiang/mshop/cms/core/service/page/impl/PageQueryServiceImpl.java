package com.mengxiang.mshop.cms.core.service.page.impl;


import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.akucun.merchant.platform.model.PageRes;
import com.akucun.merchant.platform.model.dto.req.shop.PageByUserCodeReq;
import com.akucun.merchant.platform.model.vo.shop.MerShopVO;
import com.akucun.meroperationtool.model.dto.AmbUserDetailDTO;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.service.integration.feign.merchant.MerchantClient;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;
import com.mengxiang.mshop.cms.core.model.utils.DateUtil;
import com.mengxiang.mshop.cms.core.service.page.PageQueryService;
import com.mengxiang.mshop.cms.service.facade.common.feign.page.PageManagerFeign;
import com.mengxiang.mshop.cms.service.facade.common.request.PageSearchReq;
import com.mengxiang.mshop.cms.service.facade.common.result.PageSelectResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 内容检测
 * <AUTHOR>
 */
@Service
@Slf4j
public class PageQueryServiceImpl implements PageQueryService {

    @Resource
    private PageManagerFeign pageManagerFeign;

    @Autowired
    private MerchantClient merchantClient;

    @Override
    public PageBO detail(String pageCode, String version){
        Result<PageBO> result = pageManagerFeign.detail(pageCode,version);
        log.info("[[detail]] 查询页面详情 pageCode:{},version:{},resp:{}",pageCode,version,JSON.toJSONString(result));
        return result.getData();
    }

    @Override
    public Result<Pagination<PageSelectResp>> pageSelect(PageSearchReq req) {
        PageSearchRequest request = this.buildPageSearchParam(req);
        Long total = 0L;
        List<PageSelectResp> respList = new ArrayList<>();
        try{

            if(Objects.equals(1,req.getSearchType())){
                //管理员权限查询
                request.setOwnerType(null);
                request.setOwnerIdList(null);
                request.setOwnerId(null);
            }
            Result<Pagination<PageSelectResult>> paginationResult = pageManagerFeign.pageSelectV2(request);
            if (null != paginationResult && null != paginationResult.getData() && CollectionUtils.isNotEmpty(paginationResult.getData().getResult())) {
                List<PageSelectResult> result = paginationResult.getData().getResult();
                total = paginationResult.getData().getTotal();
                result.forEach(a->{
                    PageSelectResp resp = this.convertPageResp(a);
                    respList.add(resp);
                });
            } else {
                log.warn("[pageFeign.pageSelect]查询页面列表req:{},result:{}", JSON.toJSONString(req), JSON.toJSONString(paginationResult));
            }
        }catch (Exception e){
            log.warn("[pageFeign.pageSelect]查询页面列表异常",e);
            throw new BusinessException("查询页面列表异常");
        }

        Pagination pagination = new Pagination(req.getPageIndex(),req.getPageSize(), total, respList);
        return Result.success(pagination);
    }


    @Override
    public Result<Pagination<PageSelectResp>> pageSelectByMengXiang(PageSearchReq req, UserInfo userInfo) {
        PageSearchRequest request = this.buildPageSearchParam(req);
        //根据不同身份查询
        buildReqByRole(request,userInfo,req.getSearchType());

        Long total = 0L;
        List<PageSelectResp> respList = new ArrayList<>();
        try{
            Result<Pagination<PageSelectResult>> paginationResult = pageManagerFeign.pageSelectV2(request);
            if (null != paginationResult && null != paginationResult.getData() && CollectionUtils.isNotEmpty(paginationResult.getData().getResult())) {
                List<PageSelectResult> result = paginationResult.getData().getResult();
                total = paginationResult.getData().getTotal();
                result.forEach(a->{
                    PageSelectResp resp = this.convertPageResp(a);
                    respList.add(resp);
                });
            } else {
                log.warn("[pageFeign.pageSelect]查询页面列表req:{},result:{}", JSON.toJSONString(req), JSON.toJSONString(paginationResult));
            }
        }catch (Exception e){
            log.warn("[pageFeign.pageSelect]查询页面列表异常",e);
            throw new BusinessException("查询页面列表异常");
        }

        Pagination pagination = new Pagination(req.getPageIndex(),req.getPageSize(), total, respList);
        return Result.success(pagination);
    }

    /**
     * 页面对象转换
     * @param a
     * @return
     */
    private PageSelectResp convertPageResp(PageSelectResult a) {
        PageSelectResp resp = new PageSelectResp();
        resp.setPageCode(a.getPageCode());
        resp.setPublishFlag(a.getPublishFlag());
        resp.setName(a.getName());
        resp.setCreateTime(DateUtil.dateToStrLong(a.getCreateTime()));
        resp.setUpdateTime(DateUtil.dateToStrLong(a.getUpdateTime()));
        resp.setStatus(a.getStatus());
        resp.setSubTitle(a.getSubTitle());
        resp.setTitle(a.getTitle());
        resp.setBizType(a.getBizType());
        resp.setVersion(a.getVersion());
        resp.setUpdateBy(a.getUpdateBy());
        resp.setCreateBy(a.getCreateBy());
        resp.setPageType(a.getPageType());
        resp.setFailMessage(a.getFailMessage());
        resp.setOwnerId(a.getOwnerId());
        resp.setOwnerType(a.getOwnerType());
        if(Objects.nonNull(a.getTimeConfig())
                && a.getTimeConfig().getEffectiveType() == 2
                && a.getTimeConfig().getTimeType() == 1
                && CollectionUtils.isNotEmpty(a.getTimeConfig().getTimeList())
                && a.getTimeConfig().getTimeList().size()>0){
            resp.setStartTime(a.getTimeConfig().getTimeList().get(0).getStartTime());
            resp.setEndTime(a.getTimeConfig().getTimeList().get(0).getEndTime());
        }

        return resp;
    }

    private PageSearchRequest buildPageSearchParam(PageSearchReq req){
        PageSearchRequest request = new PageSearchRequest();
        request.setCreateBy(req.getCreateBy());
        request.setPageSize(req.getPageSize());
        request.setPageIndex(req.getPageIndex());
        request.setPageCodeList(req.getPageCodeList());
        request.setChannel(req.getChannel());
        request.setCreateTimeEnd(req.getCreateTimeEnd());
        request.setCreateTimeStart(req.getCreateTimeStart());
        request.setUpdateTimeEnd(req.getUpdateTimeEnd());
        request.setUpdateTimeStart(req.getUpdateTimeStart());
        request.setName(req.getName());
        request.setStatus(req.getStatus());
        request.setSubTitle(req.getSubTitle());
        request.setTitle(req.getTitle());
        request.setTenantId(req.getTenantId());
        request.setVersion(req.getVersion());
        request.setOwnerType(req.getOwnerType());
        request.setOwnerId(req.getOwnerId());
        request.setType(req.getType());
        request.setTypeList(req.getTypeList());
        return request;
    }

    private void buildReqByRole(PageSearchRequest request,UserInfo userInfo,Integer searchType){
        request.setType(PageType.MARKET_PAGE.getType());
        if(Objects.equals(1,searchType)){
            //管理员权限查询
            return;
        }
        AmbUserDetailDTO ambUserDetailDTO = merchantClient.detailAmbUser(userInfo.getUsercode(),false);
        if(Objects.nonNull(ambUserDetailDTO)
                && CollectionUtils.isNotEmpty(ambUserDetailDTO.getRoleCodeList())
                && ambUserDetailDTO.getRoleCodeList().contains("ADMIN")){
            //判断是不是管理员，管理员看所有页面
            return;
        }

        List<String> shopCodeList = searchBDShopCode(userInfo.getUsercode());
        if(CollectionUtils.isNotEmpty(shopCodeList)){
            //判断是不是BD，BD看对应商家的页面
            request.setOwnerIdList(shopCodeList);
        }
        //默认看到平台创建的页面
        request.setOwnerType(PageOwnerType.MENGXIANG.getOwnerType());

    }

    /**
     * 查询店铺code
     * @param userCode
     * @return
     */
    private List<String> searchBDShopCode(String userCode){
        List<String> shopCodeList = Lists.newArrayList();
        int i=1;
        boolean action = false;
        do {
            PageByUserCodeReq req = new PageByUserCodeReq();
            req.setUserCodeList(Lists.newArrayList(userCode));
            req.setPageIndex(i);
            req.setPageSize(20);
            PageRes<MerShopVO> resp = merchantClient.pageByUserCode(req);
            if(Objects.nonNull(resp) && CollectionUtils.isNotEmpty(resp.getRecords())){
               List<String> result = resp.getRecords().stream().filter(Objects::nonNull)
                        .filter(x->Objects.nonNull(x.getShopCode()))
                        .map(MerShopVO::getShopCode).collect(Collectors.toList());
               if(CollectionUtils.isNotEmpty(result)){
                   shopCodeList.addAll(result);
               }
                action = resp.getHasNext();
            }
            i++;
        } while (action);
        return shopCodeList;
    }


    @Override
    public Result<Pagination<PageSelectResp>> pageSelectV2(PageSearchReq req) {
        Long total = 0L;
        List<PageSelectResp> respList = new ArrayList<>();
        try{
            PageSearchRequest request = this.buildPageSearchParam(req);
            Result<Pagination<PageSelectResult>> paginationResult = pageManagerFeign.pageSelectV2(request);
            if (null != paginationResult && null != paginationResult.getData() && CollectionUtils.isNotEmpty(paginationResult.getData().getResult())) {
                List<PageSelectResult> result = paginationResult.getData().getResult();
                total = paginationResult.getData().getTotal();
                result.forEach(a->{
                    PageSelectResp resp = this.convertPageResp(a);
                    respList.add(resp);
                });
            } else {
                log.warn("[pageFeign.pageSelectV2]查询页面列表req:{},result:{}", JSON.toJSONString(req), JSON.toJSONString(paginationResult));
            }
        }catch (Exception e){
            log.warn("[pageFeign.pageSelectV2]查询页面列表异常",e);
            throw new BusinessException("查询页面列表异常");
        }

        Pagination pagination = new Pagination(req.getPageIndex(),req.getPageSize(), total, respList);
        return Result.success(pagination);
    }
}
