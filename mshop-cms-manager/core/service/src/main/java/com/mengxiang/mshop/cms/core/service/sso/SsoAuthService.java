package com.mengxiang.mshop.cms.core.service.sso;

import com.aikucun.security.sso.permission.dto.response.Role;
import com.aikucun.security.sso.permission.dto.response.UserInfo;

import java.util.List;


/**
 * sso auth info
 *  <AUTHOR>
 */
public interface SsoAuthService {
    /**
     * 获取信息
     * @param authentication authentication
     * @return user info
     */
    UserInfo getUserInfo(String authentication);

    /**
     * roles list
     * @param authentication authentication
     * @return auth info
     */
    List<Role> getUserRoles(String authentication);

    /**
     * 是否有某个角色
     * @param authentication authentication
     * @return false or true
     */
    boolean hasAccessRoles(String authentication);
}
