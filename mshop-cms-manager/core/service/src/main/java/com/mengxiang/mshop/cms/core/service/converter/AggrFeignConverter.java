package com.mengxiang.mshop.cms.core.service.converter;

import com.mengxiang.mshop.aggr.core.model.req.base.AggrFeignBaseDTO;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AggrFeignConverter {

    @Value("${page.preview.shopId:771042619932983300}")
    private Long shopId;

    @Value("${page.preview.userId:866053648106253000}")
    private Long userId;

    @Value("${page.tenantId:151738493257170900}")
    private Long tenantId;

    public void setAggrFeignBaseDTO(AggrFeignBaseDTO dto) {
        dto.setAppId(CmsProdConstant.APP_ID);
        dto.setAppLoginChannel("akcApp");
        dto.setShopId(shopId);
        dto.setTenantId(tenantId);
        dto.setCurrentRoleType(2);
        dto.setUserId(userId);
        dto.setUserLoginType(6);
    }
}
