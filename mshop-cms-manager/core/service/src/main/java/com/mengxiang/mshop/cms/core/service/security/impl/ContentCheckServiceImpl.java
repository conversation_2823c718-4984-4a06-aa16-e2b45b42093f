package com.mengxiang.mshop.cms.core.service.security.impl;

import com.google.common.base.Preconditions;
import com.mengxiang.mshop.cms.common.service.integration.feign.security.ContentCheckClient;
import com.mengxiang.mshop.cms.core.model.request.content.ContentCheckRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import com.mengxiang.mshop.cms.core.service.security.ContentCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;



@Slf4j
@Service
public class ContentCheckServiceImpl implements ContentCheckService {
  
    @Autowired
    private ContentCheckClient contentCheckClient;
    @Value("${content.check.intervalTime:1}")
    Integer intervalTime;
    
    @Value("${content.deal.intervalTime:2}")
    Integer dealTime;
    
    @Override
    public List<ContentCheckResponse> contextCheck(ContentCheckRequest req) {
        Preconditions.checkArgument(!CollectionUtils.isEmpty(req.getContents()), "校验内容 不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getType()), "检测类型 不能为空");
        Preconditions.checkArgument(!StringUtils.isEmpty(req.getOperateBy()), "操作人 不能为空");
        List<ContentCheckResponse> resp = contentCheckClient.contextTextCheck(req);
        return resp;
    }

}
