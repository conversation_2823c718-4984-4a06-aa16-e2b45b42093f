package com.mengxiang.mshop.cms.core.service.promo;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.mshop.cms.service.facade.common.request.CouponListReq;
import com.mengxiang.mshop.cms.service.facade.common.request.PromotionActivityReq;
import com.mengxiang.mshop.cms.service.facade.common.request.PromotionCouponReq;
import com.mengxiang.mshop.cms.service.facade.common.result.PromotionActivityListResp;
import com.mengxiang.mshop.cms.service.facade.common.result.PromotionCouponResp;
import com.mengxiang.promo.service.facade.response.PromotionCouponActivityListResp;
import com.mengxiang.promo.service.facade.response.PromotionCouponListResp;

import java.util.List;

public interface PromoActivityService {

    /**
     * 查询营销活动列表
     * @param promotionCouponReq
     * @return
     */
    Pagination<PromotionActivityListResp> searchPromoActivityList(PromotionActivityReq promotionCouponReq);


    /**
     * 查询优惠券列表
     * @param promotionCouponReq
     * @return
     */
    List<PromotionCouponListResp> searchPromoCouponList(PromotionCouponReq promotionCouponReq);

    /**
     * 查询优惠券信息
     * @param list
     * @return
     */
    List<PromotionCouponResp> promoCouponListByCouponId(List<CouponListReq> list,Boolean pointsCoupon);


    /**
     * 优惠券选择校验
     * @param list
     * @return
     */
    Boolean promoCouponCheck(String promoActivityId,String promoActivityRange);
}
