package com.mengxiang.mshop.cms.core.service.promo.impl;

import com.akucun.activity.mgt.facade.stub.api.PromotionCouponRpcService;
import com.akucun.activity.mgt.facade.stub.dto.request.coupon.QueryPromotionCouponDetailWhitConditionReq;
import com.akucun.activity.mgt.facade.stub.dto.response.coupon.PromotionCouponDetailRes;
import com.akucun.activity.mgt.facade.stub.enums.PromotionActivityStatusEnum;
import com.akucun.activity.mgt.facade.stub.enums.PromotionCouponSceneEnum;
import com.akucun.activity.mgt.facade.stub.enums.PromotionCouponTypeEnum;

import com.akucun.common.Pagination;
import com.akucun.common.Result;
import com.akucun.mshop.common.util.BeanCopyUtil;
import com.akucun.sp.model.dto.mgt.CouponTocPageSearcher;
import com.akucun.sp.model.vo.mgt.CouponTocListVO;
import com.akucun.sp.stub.api.SpCouponSearcherService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mengxiang.mshop.cms.core.service.promo.HotSaleSpDetailService;
import com.mengxiang.mshop.cms.service.facade.common.result.HotSaleSpInfoThirdQueryDTO;
import com.mengxiang.mshop.cms.service.facade.common.result.HotSaleSpInfoThirdResVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HotSaleSpDetailServiceImpl implements HotSaleSpDetailService {

    @Autowired
    private PromotionCouponRpcService promotionCouponRpcService;
    @Autowired
    private SpCouponSearcherService spCouponSearcherService;

    @Value("${hotsale.search.sp.status:ON}")
    private String checkSearchSpStatus;

    @Override
    public Result<Pagination<HotSaleSpInfoThirdResVO>> querySpInfoFromMerchant(HotSaleSpInfoThirdQueryDTO dto) {
        QueryPromotionCouponDetailWhitConditionReq queryBean = BeanCopyUtil.copy(dto, QueryPromotionCouponDetailWhitConditionReq.class);
        if(Objects.nonNull(dto.getActivityEndTime())){
            queryBean.setActivityEndTime(new Date(dto.getActivityEndTime()));
        }
        if(Objects.nonNull(dto.getActivityStartTime())){
            queryBean.setActivityStartTime(new Date(dto.getActivityStartTime()));
        }
        queryBean.setPromotionStatus(PromotionActivityStatusEnum.ACTIVE.getCode());
        queryBean.setCouponType(PromotionCouponTypeEnum.BUSINESS.getCode());
        queryBean.setScene(PromotionCouponSceneEnum.UNLIMITED.getCode());
        queryBean.setPageIndex(dto.getPageIndex());
        queryBean.setPageSize(dto.getPageSize());
        Result<Pagination<PromotionCouponDetailRes>> paginationResult = promotionCouponRpcService.queryPromotionCouponDetailWhitCondition(queryBean);
        log.warn("querySpInfoFromMerchant req:{},resp:{}", JSON.toJSONString(queryBean),JSON.toJSONString(paginationResult));
        if(paginationResult.isSuccess()){
            List<HotSaleSpInfoThirdResVO> collect = paginationResult.getData().getResult().stream().map(vo ->{
                HotSaleSpInfoThirdResVO res = BeanCopyUtil.copy(vo, HotSaleSpInfoThirdResVO.class);
                res.setPromotionCode(Objects.isNull(vo.getPromotionCode())?"":String.valueOf(vo.getPromotionCode()));
                res.setCouponSysId(Objects.isNull(vo.getCouponSysId())?"":String.valueOf(vo.getCouponSysId()));
                res.setActivityId(Objects.isNull(vo.getActivityId())?"":String.valueOf(vo.getActivityId()));
                res.setActivityBrandId(Objects.isNull(vo.getActivityBrandId())?"":String.valueOf(vo.getActivityBrandId()));
                return res;
            }).collect(Collectors.toList());
            Pagination<HotSaleSpInfoThirdResVO> hotSaleSpInfoThirdResPagination = new Pagination<>(collect, paginationResult.getData().getTotal(), paginationResult.getData().getPageSize(), paginationResult.getData().getPageIndex());
            return Result.success(hotSaleSpInfoThirdResPagination);
        }
        return Result.error(paginationResult.getMessage());
    }

    @Override
    public Result<Pagination<HotSaleSpInfoThirdResVO>> querySpInfoFromSpPlat(HotSaleSpInfoThirdQueryDTO dto) {
        CouponTocPageSearcher searcher = new CouponTocPageSearcher();

        searcher.setName(dto.getCouponName());
        searcher.setCouponNumber(dto.getCouponSysId()!=null?String.valueOf(dto.getCouponSysId()):null);
        searcher.setPageIndex(dto.getPageIndex());
        searcher.setPageSize(dto.getPageSize());
        searcher.setSource(0);
        //已激活
        searcher.setStatus(1);
        searcher.setSceneList(Lists.newArrayList(0,10));
        if(dto.getPromotionStatus() != null){
            searcher.setStatus(dto.getPromotionStatus());
        }
        if (dto.getCouponUse() != null) {
            searcher.setCouponUse(dto.getCouponUse());
        }
        Result<Pagination<CouponTocListVO>> paginationResult = spCouponSearcherService.pageSearchV2(searcher);
        log.warn("querySpInfoFromSpPlat req:{},resp:{}", JSON.toJSONString(searcher),JSON.toJSONString(paginationResult));
        if(paginationResult.isSuccess()){
            List<HotSaleSpInfoThirdResVO> collect = paginationResult.getData().getResult().stream().map(vo -> {
                HotSaleSpInfoThirdResVO single = new HotSaleSpInfoThirdResVO();
                single.setCouponSysId(Objects.isNull(vo.getId())?"":vo.getId());
                single.setCouponName(vo.getName());
                single.setTheBusinessSide(vo.getTheBusinessSide());
                single.setCouponAmount(new BigDecimal(vo.getAmount()).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                single.setCouponLimitAmount(new BigDecimal(vo.getThresholdAmount()).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                single.setUseStartTimeStr(vo.getUseTimeDesc());
                single.setShareTimeDesc(vo.getShareTimeDesc());
                single.setPromotionStatus(vo.getStatus());
                single.setPromotionStatusDesc(Objects.equals(1, vo.getStatus()) ? "已激活" : "未激活");
                single.setInitialNum(vo.getInitialNum());
                single.setCurrentNum(vo.getCurrentNum());
                single.setSpActivityId(Objects.isNull(vo.getSpActivityId())?"":String.valueOf(vo.getSpActivityId()));
                single.setPayer(vo.getPayer());
                return single;
            }).collect(Collectors.toList());
            Pagination<HotSaleSpInfoThirdResVO> hotSaleSpInfoThirdResPagination = new Pagination<>(collect, paginationResult.getData().getTotal(), paginationResult.getData().getPageSize(), paginationResult.getData().getPageIndex());
            return Result.success(hotSaleSpInfoThirdResPagination);
        }
        return Result.error(paginationResult.getMessage());
    }
}
