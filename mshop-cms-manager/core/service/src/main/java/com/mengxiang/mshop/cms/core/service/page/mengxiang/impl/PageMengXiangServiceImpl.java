package com.mengxiang.mshop.cms.core.service.page.mengxiang.impl;

import com.akucun.mshop.common.util.BeanCopyUtil;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.base.Preconditions;
import com.mengxiang.base.share.service.facade.common.enums.CarrierEnum;
import com.mengxiang.base.share.service.facade.common.enums.ShareTypeEnum;
import com.mengxiang.base.share.service.facade.common.enums.TargetChannelEnum;
import com.mengxiang.base.share.service.facade.common.enums.UserTypeEnum;
import com.mengxiang.base.share.service.facade.common.request.GenerateShortCodeLinkRequest;
import com.mengxiang.base.share.service.facade.common.request.UserInfo;
import com.mengxiang.base.share.service.facade.common.result.GenerateShortCodeLinkVO;
import com.mengxiang.merchant.platform.query.service.facade.common.result.QueryShopResult;
import com.mengxiang.mshop.cms.common.service.integration.feign.merchant.MerchantClient;
import com.mengxiang.mshop.cms.common.service.integration.feign.share.ShortCodeServiceClient;
import com.mengxiang.mshop.cms.core.service.page.mengxiang.PageMengXiangService;
import com.mengxiang.mshop.cms.service.facade.common.result.HotSaleSpInfoThirdResVO;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.MerchantShopResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.PageMengXiangPrewLink;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;


/**
 * 梦饷页面组件服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class PageMengXiangServiceImpl implements PageMengXiangService {
    
    @Autowired
    private ShortCodeServiceClient shortCodeServiceClient;

    @Value("${mengxiang.micro.app.url:http://h5shop-stable.akucun.com/mx-shop-micro?customPageCode=}")
    private String mengXiangMicroUrlByApp;

    @Value("${mengxiang.micro.h5.url:http://h5shop-stable.akucun.com/mx-shop-micro?customPageCode=}")
    private String mengXiangMicroUrlByH5;

    @Value("${mengxiang.micro.mini.url:http://fe-static.akcstable.com/mx-shop-micro?customPageCode=}")
    private String mengXiangMicroUrlByMini;


    @Value("${mengxiang.default.shopId:1}")
    private Long shopId;

    @Autowired
    private MerchantClient merchantClient;


    @Override
    public MerchantShopResp getShopInfoByShopCode (String shopCode){
        Preconditions.checkArgument(StringUtils.isNotEmpty(shopCode), "店铺code 不能为空");
        QueryShopResult queryShopInfo = merchantClient.getShopInfoByShopCode(shopCode);
        if (Objects.nonNull(queryShopInfo)) {
            MerchantShopResp merchantShopResp = BeanCopyUtil.copy(queryShopInfo, MerchantShopResp.class);
            return merchantShopResp;
        }
        return null;
    }


    @Override
    public Map<String, PageMengXiangPrewLink> preview(String staffId, String pageCode, String version) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(pageCode), "页面code 不能为空");
        Preconditions.checkArgument(StringUtils.isNotEmpty(version), "页面版本 不能为空");
        Map<String, PageMengXiangPrewLink> resp = new HashedMap();
        PageMengXiangPrewLink minilink = new PageMengXiangPrewLink();
        //店主
        String rMiniPreviewLink = getPreviewLink(null, TargetChannelEnum.MINI, UserTypeEnum.SELLER, pageCode, version);
        minilink.setRShopPreviewLink(rMiniPreviewLink);
        //店长
        minilink.setEShopPreviewLink(rMiniPreviewLink);
        //c
        String cMiniPreviewLink = getPreviewLink(shopId, TargetChannelEnum.MINI, UserTypeEnum.USER, pageCode, version);
        minilink.setCShopPreviewLink(cMiniPreviewLink);
        resp.put("MINI", minilink);

        PageMengXiangPrewLink h5link = new PageMengXiangPrewLink();
        //店主
        String rH5link = getPreviewLink(null, TargetChannelEnum.H5, UserTypeEnum.SELLER, pageCode, version);
        h5link.setRShopPreviewLink(rH5link);
        h5link.setEShopPreviewLink(rH5link);
        //c
        String cH5link = getPreviewLink(shopId, TargetChannelEnum.H5, UserTypeEnum.USER, pageCode, version);
        h5link.setCShopPreviewLink(cH5link);
        resp.put("H5", h5link);

        PageMengXiangPrewLink appLink = new PageMengXiangPrewLink();
        String url = new StringBuilder().append(mengXiangMicroUrlByApp).append(pageCode).append("&version=").append(version)
                .append("&preview=true").toString();
        appLink.setCShopPreviewLink(null);
        appLink.setRShopPreviewLink(url);
        appLink.setEShopPreviewLink(null);
        resp.put("APP", appLink);
        return resp;
    }
    
    @Override
    public Map<String, String> generatePageUrl(String pageCode) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(pageCode), "pageCode 不能为空");
        String appUrl = mengXiangMicroUrlByApp + pageCode;
        String h5Url = mengXiangMicroUrlByH5 + pageCode;
        String miniUrl = mengXiangMicroUrlByMini + pageCode;
        Map<String, String> resp = new HashedMap();
        resp.put("APP", appUrl);
        resp.put("H5", h5Url);
        resp.put("MINI", miniUrl);
        return resp;
    }
    
    
    private String getPreviewLink(Long shopId, TargetChannelEnum channel, UserTypeEnum userType, String pageCode,
            String version) {
        GenerateShortCodeLinkRequest req = new GenerateShortCodeLinkRequest();
        req.setTargetChannel(channel.getCode());
        req.setCarrier(CarrierEnum.POSTER.getCode());
        req.setContentType(ShareTypeEnum.CONFERENCE3.getDesc());
        //页面参数
        Map<String, String> pageParam = new HashedMap();
        pageParam.put("preview", "true");
        pageParam.put("customPageCode", pageCode);
        pageParam.put("version", version);
        req.setPageParam(pageParam);
        
        UserInfo userInfo = new UserInfo();
        userInfo.setShopId(shopId);
        userInfo.setUserType(userType.name());
        req.setUserInfo(userInfo);
        GenerateShortCodeLinkVO link = shortCodeServiceClient.generateShortCodeLink(req);
        if (null != link) {
            return link.getUrl();
        }
        return "";
    }

    @Override
    public JSONObject findShareText(){
        Config config = ConfigService.getConfig("application");
        String jsonString = config.getProperty("share.text","");
        if(StringUtils.isNotBlank(jsonString)){
            return JSONObject.parseObject(jsonString);
        }
        return null;
    }

    @Override
    public String findShareTextByCategory(String category){
        Config config = ConfigService.getConfig("application");
        return config.getProperty("share.text."+category,"");

    }
}
