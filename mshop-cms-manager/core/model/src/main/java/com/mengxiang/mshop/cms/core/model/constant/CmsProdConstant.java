package com.mengxiang.mshop.cms.core.model.constant;

public interface CmsProdConstant {
    /**
     * 应用ID
     */
    String APP_ID = "mshop-cms-manager";
    /**
     * 对外服务接口根路径
     */
    String OUTER_API_ROOT_URL = "/api/mshop/mshop-cms-manager/outer";
    /**
     * 对内网提供feign接口根路径(没有token校验)
     */
    String INNER_API_ROOT_URL = "/api/mshop/mshop-cms-manager/feign";

    /**
     * http请求头中key定义
     */
    String HEADER_GATEWAY_KEY = "app-request-id";
    String HEADER_APP_ID = "app-id";
    String HEADER_SHOP_ID = "shop-id";
    String HEADER_TENANT_ID = "tenant-id";
    String HEADER_CURRENT_ROLE_TYPE = "current-role-type";
    String HEADER_USER_ID = "user-id";
    String HEADER_CONDITIONAL_LOG_SWITCH = "conditional-log-switch";
    // 请求头部
    public final static String APP_OS_REQUEST_HEADER = "AKC-OS";

    public final static String APP_VERSION_REQUEST_HEADER = "AKC-APP-VERSION";

    //梦饷云后台
    String MANAGE_LOGIN_TOKEN = APP_ID + ":MANAGE:LOGIN:";
}
