package com.mengxiang.mshop.cms.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 2023/3/8
 * @Description:
 */
@Getter
public enum LoginPageChannel {

    /**
     * 商户云-店铺装修
     */
    MERCHANTDECORATE("10", "商户云-店铺装修"),
    /**
     * 商户云-会场装修
     */
    MERCHANTCONFERENCE("11", "商户云-会场装修"),
    /**
     * 风火轮-会场3.0
     */
    SUPERBRAND("20", "风火轮-会场3.0"),
    /**
     * 梦饷云-企业饷店
     */
    CLOUND("30", "梦饷云-企业饷店");

    private String channel;
    private String desc;

    LoginPageChannel(String channel, String desc) {
        this.channel = channel;
        this.desc = desc;
    }
}
