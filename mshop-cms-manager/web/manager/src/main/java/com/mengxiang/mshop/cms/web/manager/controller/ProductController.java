package com.mengxiang.mshop.cms.web.manager.controller;

import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.aggr.core.model.vo.product.ProductListVO;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.result.SafeModelRuleLabelResult;
import com.mengxiang.mshop.cms.core.service.product.ProductService;
import com.mengxiang.mshop.cms.service.facade.common.request.ProductReq;
import com.mengxiang.mshop.cms.service.facade.common.request.TenantUser;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "商品服务", tags = {"商品服务"})
public class ProductController {

    @Autowired
    private ProductService productService;
    
    @ApiOperation("查询商品列表")
    @PostMapping("/search/productList")
    public Result<Pagination<ProductListVO>> productList(
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
            @RequestBody ProductReq req) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        String shopCode = userInfo.getShopCode();
        req.setShopCode(shopCode);
        return Result.success(productService.productList(req));
    }

    @ApiOperation("运营-查询商品列表")
    @PostMapping("/mengxiang/search/productList")
    public Result<Pagination<ProductListVO>> productListByMengxiang(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestBody ProductReq req) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(productService.productList(req));
    }
    @ApiOperation("梦饷云-查询商品列表")
    @PostMapping("/saas/search/productList")
    public Result<Pagination<ProductListVO>> productListBySaas(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestBody ProductReq req) {
        if(null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(productService.productList(req));
    }

    @ApiOperation("查询商品营销规则标签")
    @GetMapping("/search/findSafeModelRuleLabel")
    public Result<List<SafeModelRuleLabelResult>> findSafeModelRuleLabel(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo manageUser,
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo merchantUserInfo,
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser) {
        if(null==manageUser && null==merchantUserInfo && null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }

        return Result.success(productService.findSafeModelRuleLabel());
    }
}
