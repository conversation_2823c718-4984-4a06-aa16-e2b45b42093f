package com.mengxiang.mshop.cms.web.manager.config;

import com.mengxiang.base.common.model.exception.BusinessException;
import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.mshop.cms.core.service.sso.SsoAuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ModelAttribute;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 风火轮
 */
@Slf4j
@ControllerAdvice
public class ManagementUserAdvice {

    @Autowired
    private SsoAuthService ssoAuthService;

    private static final String X_AUTHENTICATION = "X-AUTHENTICATION";

    private static final String IN_SERVICE = "INSERVICE";


    @ModelAttribute("manageUser")
    public UserInfo tenantLogin(HttpServletRequest request) throws BusinessException {
        //sso的登录信息
        String xAuthToken = request.getHeader(X_AUTHENTICATION);
        if (StringUtils.isEmpty(xAuthToken)) {
            return null;
        }

        try {
            UserInfo ssoAuthServiceUserInfo = ssoAuthService.getUserInfo(xAuthToken);
            if (Objects.isNull(ssoAuthServiceUserInfo)) {
                throw new BusinessException("用户不存在或登录信息已过期");
            }

            //权限验证
            //if(!checkPermission(ssoAuthServiceUserInfo, xAuthToken)){
            //    throw new BusinessException("用户访问权限不支持");
            //}
            return ssoAuthServiceUserInfo;
        } catch (Exception e) {
            throw new BusinessException("用户不存在或登录信息已过期");
        }
    }
    
    
    /**
     * 校验权限
     */
    private boolean checkPermission(UserInfo userInfo, String xAuthToken) {
        if(!IN_SERVICE.equals(userInfo.getStatus())) {
            Logger.warn("非在职人员：{}, {}", userInfo.getRealname(), userInfo.getUsername());
            return false;
        }

        boolean hasAccessRoles = ssoAuthService.hasAccessRoles(xAuthToken);
        if(!hasAccessRoles) {
            Logger.warn("该用户没有管理员角色权限：{}, {}", userInfo.getRealname(), userInfo.getUsername());
            return false;
        }
        return true;
    }
}
