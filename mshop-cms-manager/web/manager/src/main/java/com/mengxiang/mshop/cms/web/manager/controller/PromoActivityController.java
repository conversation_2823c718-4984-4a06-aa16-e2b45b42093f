
package com.mengxiang.mshop.cms.web.manager.controller;

import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.enums.LoginPageChannel;
import com.mengxiang.mshop.cms.core.service.promo.HotSaleSpDetailService;
import com.mengxiang.mshop.cms.core.service.promo.PromoActivityService;
import com.mengxiang.mshop.cms.service.facade.common.request.CouponListReq;
import com.mengxiang.mshop.cms.service.facade.common.request.PromotionActivityReq;
import com.mengxiang.mshop.cms.service.facade.common.request.PromotionCouponReq;
import com.mengxiang.mshop.cms.service.facade.common.result.HotSaleSpInfoThirdQueryDTO;
import com.mengxiang.mshop.cms.service.facade.common.result.HotSaleSpInfoThirdResVO;
import com.mengxiang.mshop.cms.service.facade.common.result.PromotionActivityListResp;
import com.mengxiang.mshop.cms.service.facade.common.result.PromotionCouponResp;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;
import com.mengxiang.promo.service.facade.response.PromotionCouponActivityListResp;
import com.mengxiang.promo.service.facade.response.PromotionCouponListResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "营销服务", tags = {"营销服务"})
public class PromoActivityController {

    @Autowired
    private PromoActivityService promoActivityService;

    @Autowired
    private HotSaleSpDetailService hotSaleSpDetailService;

    @ApiOperation(value = "查询营销活动列表")
    @PostMapping("/search/promoActivityList")
    public Result<Pagination<PromotionActivityListResp>> searchPromoActivityList(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo merchantUserInfo,
            @RequestHeader(value = "loginPageChannel") String loginPageChannel,
            @RequestBody PromotionActivityReq req) {
        if(Objects.nonNull(merchantUserInfo)
                && StringUtils.isNotBlank(loginPageChannel)
                && Objects.equals(loginPageChannel, LoginPageChannel.MERCHANTCONFERENCE.getChannel())){
            //商家调用要隔离
            req.setPromoActivityVersion("NEW");
            req.setPromoActivityOwner("MERCHANT");
            req.setPromoActivityOwnerId(merchantUserInfo.getShopCode());
        }
        return Result.success(promoActivityService.searchPromoActivityList(req));
    }

    @ApiOperation(value = "查询优惠券列表")
    @PostMapping("/search/promoCouponList")
    public Result<List<PromotionCouponListResp>> searchPromoCouponList(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestBody PromotionCouponReq req) {

        return Result.success(promoActivityService.searchPromoCouponList(req));
    }

    @ApiOperation(value = "根据优惠券id查询优惠券列表")
    @PostMapping("/search/promoCouponListByCouponId")
    public Result<List<PromotionCouponResp>> promoCouponListByCouponId(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestBody List<CouponListReq> list,
            @RequestParam(value = "pointsCoupon",required = false,defaultValue = "false") Boolean pointsCoupon) {

        return Result.success(promoActivityService.promoCouponListByCouponId(list,pointsCoupon));
    }

    @ApiOperation(value = "优惠券选择校验")
    @GetMapping("/search/promoCouponCheck")
    public Result<Boolean> promoCouponCheck(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestParam("promoActivityId") String promoActivityId, @RequestParam("promoActivityRange") String promoActivityRange) {

        return Result.success(promoActivityService.promoCouponCheck(promoActivityId,promoActivityRange));
    }

    @PostMapping(value = "/merchant/page")
    @ApiOperation(value = "大促会场优惠券商家券查询")
    public com.akucun.common.Result<com.akucun.common.Pagination<HotSaleSpInfoThirdResVO>>
    pageMerchant(@RequestBody HotSaleSpInfoThirdQueryDTO dto) {
        return hotSaleSpDetailService.querySpInfoFromMerchant(dto);
    }

    @PostMapping(value = "/spPlat/page")
    @ApiOperation(value = "大促会场优惠券平台券查询")
    public com.akucun.common.Result<com.akucun.common.Pagination<HotSaleSpInfoThirdResVO>>
    pageSpPlat(@RequestBody HotSaleSpInfoThirdQueryDTO dto) {
        return hotSaleSpDetailService.querySpInfoFromSpPlat(dto);
    }
}