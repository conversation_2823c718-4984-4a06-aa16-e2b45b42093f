package com.mengxiang.mshop.cms.web.manager.controller.securty;

import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.request.content.ContentCheckRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import com.mengxiang.mshop.cms.core.service.security.ContentCheckService;
import com.mengxiang.mshop.cms.service.facade.common.request.TenantUser;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.omg.CORBA.SystemException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "内容检测", tags = {"内容检测"})
public class ContentCheckController {
    
    @Autowired
    private ContentCheckService contentCheckService;
    
    @ApiOperation("内容检查接口")
    @PostMapping("/api/content/contextCheck")
    public Result<List<ContentCheckResponse>> contextTextCheck(
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
            @RequestBody ContentCheckRequest req) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        req.setOperateBy(userInfo.getUserId().toString());
        return Result.success(contentCheckService.contextCheck(req));
    }

    @ApiOperation("运营-内容检查接口")
    @PostMapping("/api/content/mengxiang/contextCheck")
    public Result<List<ContentCheckResponse>> contextTextCheckByMengxiang(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestBody ContentCheckRequest req) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        req.setOperateBy(userInfo.getId());
        return Result.success(contentCheckService.contextCheck(req));
    }
    @ApiOperation("梦饷云-内容检查接口")
    @PostMapping("/api/content/saas/contextCheck")
    public Result<List<ContentCheckResponse>> contextTextCheckBySaas(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestBody ContentCheckRequest req) {
        if(null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }
        req.setOperateBy(String.valueOf(tenantUser.getTenantId()));
        return Result.success(contentCheckService.contextCheck(req));
    }
}
