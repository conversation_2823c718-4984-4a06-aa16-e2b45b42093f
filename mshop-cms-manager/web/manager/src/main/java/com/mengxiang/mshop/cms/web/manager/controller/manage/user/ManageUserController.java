package com.mengxiang.mshop.cms.web.manager.controller.manage.user;

import com.aikucun.security.sso.authen.core.dto.response.SsoAuthenResult;
import com.aikucun.security.sso.permission.dto.base.SsoPermissionResult;
import com.aikucun.security.sso.permission.dto.response.Resource;
import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.mengxiang.mshop.cms.core.service.user.manage.ManageUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @menu 梦饷工作台 登陆用户管理
 **/
@Api(tags = "梦饷工作台 登陆用户管理")
@RestController
@RequestMapping("/api/manage/userInfo")
public class ManageUserController {

    @Autowired
    private ManageUserService baseService;

    @ApiOperation(value="获取登陆用户基本信息")
    @GetMapping("/get-user")
    public SsoPermissionResult<UserInfo> detail(HttpServletRequest request){
        return baseService.getLoginUser(request);
    }

    @ApiOperation(value="获取登陆用户的菜单和按钮")
    @GetMapping("/get-user-menu-button")
    public SsoPermissionResult<List<Resource>> getUserMenuAndButton(HttpServletRequest request){
        return baseService.getMenuAndButton(request);
    }

    @ApiOperation(value="登出")
    @GetMapping("/logout")
    public SsoAuthenResult<String> logout(HttpServletRequest request, HttpServletResponse response){
        return baseService.ssoLogout(request,response);
    }
}
