package com.mengxiang.mshop.cms.web.manager.config;

import com.mengxiang.mshop.cms.core.service.user.merchant.MerchantUserInfoService;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ModelAttribute;

import javax.servlet.http.HttpServletRequest;


/**
 * 商户
 */
@Slf4j
@ControllerAdvice
public class MerchantUserAdvice {
    
    @Autowired
    private MerchantUserInfoService merchantUserInfoService;

    @ModelAttribute("merchantUserInfo")
    public MerchantUserInfo getMyUserInfo(HttpServletRequest request) {
        return merchantUserInfoService.queryUserInfoByToken(request);
    }
}
