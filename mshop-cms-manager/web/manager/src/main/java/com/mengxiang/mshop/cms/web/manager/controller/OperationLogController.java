package com.mengxiang.mshop.cms.web.manager.controller;

import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.service.page.OperationLogService;
import com.mengxiang.mshop.cms.service.facade.common.request.OperationLogReq;
import com.mengxiang.mshop.cms.service.facade.common.result.OperationLogResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 操作记录
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(tags = "操作记录")
public class OperationLogController {

    @Autowired
    private OperationLogService operationLogService;

    @ApiOperation(value = "操作记录分页查询")
    @PostMapping(value = "/api/operationLog/page")
    public Result<Pagination<OperationLogResp>> operationLogPage(@RequestBody OperationLogReq req) {
        req.setBizType(1);
        return operationLogService.operationLogPage(req);
    }


}
