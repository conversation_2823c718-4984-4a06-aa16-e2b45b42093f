package com.mengxiang.mshop.cms.web.manager.controller;

import com.alibaba.csp.sentinel.util.AssertUtil;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.service.resource.ResourceService;
import com.mengxiang.mshop.cms.service.facade.common.request.AggrDetailNavigationResourceReq;
import com.mengxiang.mshop.cms.service.facade.common.request.AggrSaveNavigationResourceReq;
import com.mengxiang.mshop.cms.service.facade.common.request.NavigationResourceReq;
import com.mengxiang.mshop.cms.service.facade.common.request.TenantUser;
import com.mengxiang.mshop.cms.service.facade.common.result.AggrDetailNavigationResourceResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "资源位服务", tags = {"资源位服务"})
public class ResourceController {

    @Autowired
    private ResourceService resourceService;

    @ApiOperation("保存导航")
    @PostMapping("/resource/navigation/save")
    public Result<Void> saveNavigation(@ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestBody List<NavigationResourceReq> navigationResourceList) {
        if(null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }
        if(CollectionUtils.isEmpty(navigationResourceList)){
            return Result.error("导航配置不能为空");
        }
        if(navigationResourceList.size() > 10){
            return Result.error("导航配置不能超过10条");
        }
        resourceService.saveNavigation(navigationResourceList,null,tenantUser);
        return Result.success();
    }


    @ApiOperation("查询导航")
    @PostMapping("/resource/navigation/detail")
    public Result<List<NavigationResourceReq>> navigationDetail(@ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser) {
        if(null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(resourceService.navigationDetail(tenantUser));
    }


    @ApiOperation("saas聚合保存导航")
    @PostMapping("/resource/navigation/aggrSave")
    public Result<Void> aggrSaveNavigation(@RequestBody AggrSaveNavigationResourceReq aggrSaveNavigationResourceReq, @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser) {
        AssertUtil.isTrue(!Objects.isNull(tenantUser), "用户未登录");
        AssertUtil.isTrue(!Objects.isNull(aggrSaveNavigationResourceReq), "导航配置不能为空");
        resourceService.aggrSaveNavigation(tenantUser, aggrSaveNavigationResourceReq);
        return Result.success();
    }

    @ApiOperation("saas聚合查询导航")
    @PostMapping("/resource/navigation/aggrDetail")
    public Result<AggrDetailNavigationResourceResp> aggrNavigationDetail(@RequestBody AggrDetailNavigationResourceReq aggrDetailNavigationResourceReq,@ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser) {
        return Result.success(resourceService.aggrNavigationDetail(tenantUser, aggrDetailNavigationResourceReq));
    }
}
