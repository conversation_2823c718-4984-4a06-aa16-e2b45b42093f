package com.mengxiang.mshop.cms.web.manager.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2023-04-19
 */
@Component
public class RedisConfig {
    

//    @Bean("stringRedisTemplate")
//    @Primary
//    public RedisTemplate<String, String> stringRedisTemplate(RedisConnectionFactory factory) {
//
//        RedisTemplate<String, String> template = new RedisTemplate<>();
//        // 配置连接工厂
//        template.setConnectionFactory(factory);
//
//        RedisSerializer<String> stringSerializer = new StringRedisSerializer();
//        template.setKeySerializer(stringSerializer);
//        template.setValueSerializer(stringSerializer);
//        template.setHashKeySerializer(stringSerializer);
//        template.setHashValueSerializer(stringSerializer);
//        return template;
//    }

}
