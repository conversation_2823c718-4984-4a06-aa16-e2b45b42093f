package com.mengxiang.mshop.cms.web.manager.controller;

import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.aggr.core.model.vo.activity.ActivityListVO;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.request.content.ContentCheckRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import com.mengxiang.mshop.cms.core.service.activity.ActivityService;
import com.mengxiang.mshop.cms.core.service.page.PageQueryService;
import com.mengxiang.mshop.cms.service.facade.common.request.ActivityReq;
import com.mengxiang.mshop.cms.service.facade.common.request.PageSearchReq;
import com.mengxiang.mshop.cms.service.facade.common.request.TenantUser;
import com.mengxiang.mshop.cms.service.facade.common.result.PageSelectResp;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "活动服务", tags = {"活动服务"})
public class ActivityController {

    @Autowired
    private ActivityService activityService;

    @ApiOperation("查询活动列表")
    @PostMapping("/search/activityList")
    public Result<Pagination<ActivityListVO>> activityList(
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
        @RequestBody ActivityReq req) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        String shopCode = userInfo.getShopCode();
        req.setShopCode(shopCode);
        return Result.success(activityService.activityList(req));
    }

    @ApiOperation("运营-查询活动列表")
    @PostMapping("/mengxiang/search/activityList")
    public Result<Pagination<ActivityListVO>> activityListByMengxiang(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestBody ActivityReq req) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(activityService.activityList(req));
    }
    @ApiOperation("梦饷云-查询活动列表")
    @PostMapping("/saas/search/activityList")
    public Result<Pagination<ActivityListVO>> activityListBySaas(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestBody ActivityReq req) {
        if(null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(activityService.activityList(req));
    }
}
