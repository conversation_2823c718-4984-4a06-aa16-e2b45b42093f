package com.mengxiang.mshop.cms.web.manager.controller;

import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.enums.PageInstanceStatusEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.service.page.PageQueryService;
import com.mengxiang.mshop.cms.service.facade.common.request.PageSearchReq;
import com.mengxiang.mshop.cms.service.facade.common.request.TenantUser;
import com.mengxiang.mshop.cms.service.facade.common.result.PageSelectResp;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;


/**
 * <AUTHOR>
 * @menu 运营-页面搭建
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "查询页面列表", tags = {"查询页面列表"})
public class PageQueryController {
    @Autowired
    private PageQueryService pageQueryService;


    @ApiOperation(value = "查询租户搭建的页面已发布页面")
    @PostMapping(value = "/api/page/query/pageByTenant")
    public Result<Pagination<PageSelectResp>> pageByTenant(@ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
                                                         @RequestBody PageSearchReq req) {
        if (null == tenantUser) {
            return Result.error("用户未登录");
        }
        req.setTenantId(String.valueOf(tenantUser.getTenantId()));
        req.setOwnerType(PageOwnerType.SAAS_TENANT.getOwnerType());
        req.setStatus(PageInstanceStatusEnum.PUBLISH.getCode());
        return pageQueryService.pageSelectV2(req);
    }



    @ApiOperation(value = "查询会场3.0已发布页面")
    @PostMapping(value = "/api/page/query/pageByMarketPage")
    public Result<Pagination<PageSelectResp>> pageByMarketPage(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo manageUser,
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo merchantUserInfo,
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
                                                           @RequestBody PageSearchReq req) {
        if(null==manageUser && null==merchantUserInfo && null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }

        req.setType(PageType.MARKET_PAGE.getType());
        req.setStatus(PageInstanceStatusEnum.PUBLISH.getCode());
        return pageQueryService.pageSelectV2(req);
    }

}
