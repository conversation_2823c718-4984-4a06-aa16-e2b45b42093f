package com.mengxiang.mshop.cms.web.manager.controller;

import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.request.workflow.WorkflowInfoRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.WorkflowInfoResp;
import com.mengxiang.mshop.cms.core.service.page.WorkbenchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "工作流服务", tags = {"工作流服务"})
public class WorkbenchController {


    @Autowired
    private WorkbenchService workbenchService;


    @ApiOperation("查询工作流信息")
    @PostMapping("/api/page/mengxiang/findWorkflowInfo")
    public Result<WorkflowInfoResp> findWorkflowInfo(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestBody WorkflowInfoRequest req

    ) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        Result<WorkflowInfoResp> result = workbenchService.findWorkflowInfo(req);

        return result;
    }

}
