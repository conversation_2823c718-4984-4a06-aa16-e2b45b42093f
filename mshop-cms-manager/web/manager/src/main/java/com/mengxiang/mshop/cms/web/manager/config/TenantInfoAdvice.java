package com.mengxiang.mshop.cms.web.manager.config;

import com.akucun.auth.core.constant.AuthBaseConstant;
import com.akucun.auth.core.utils.AuthUtils;
import com.akucun.auth.facade.stub.entity.vo.TenantUserVO;
import com.mengxiang.mshop.cms.service.facade.common.request.TenantUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ModelAttribute;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@Slf4j
@ControllerAdvice
public class TenantInfoAdvice {
    /**
     * 不需要执行获取user的路径列表
     */
    @Value("${UserInfoAdvice.noNeedLoginUri:/swagger,/v2/api-docs,/slb/health,/error,/api/saas/backend/ajmd/bi/getRadioSalesList,/api/saas/backend/admin}")
    private String notLoginInterceptPaths;

    @Resource
    private AuthUtils authUtils;

    @ModelAttribute("tenantUser")
    public TenantUser tenantLogin(HttpServletRequest request) {
        if (notLoginIntercept(request.getRequestURI())) {
            return null;
        }

        //移除用户信息
        String token = request.getHeader(AuthBaseConstant.JQ_AUTH_TOKEN);
        String requestChannel = request.getHeader(AuthBaseConstant.APP_LOGIN_CHANNEL);
        if (StringUtils.isEmpty(token) || StringUtils.isEmpty(requestChannel)) {
            if (!request.getRequestURL().toString().contains("/slb/health") && !request.getRequestURL().toString().contains("/error")) {
                log.info("token信息不存在：URL = " + request.getRequestURL().toString());
            }
            return null;
        }
        try {
            //租户鉴权信息
            TenantUserVO tenantUserVO = authUtils.getUserBasicSaaSBackend(requestChannel, token);
            if (Objects.isNull(tenantUserVO)) {
                log.info("租户鉴权信息不存在：token = " + token + ",channel=" + requestChannel);
                return null;
            }
            TenantUser tenantUser = new TenantUser();
            tenantUser.setTenantId(tenantUserVO.getTenantId());
            tenantUser.setTenantName(tenantUserVO.getTenantName());
            tenantUser.setUserName(tenantUserVO.getUserName());
            tenantUser.setTenanType(tenantUserVO.getTenantType());
            tenantUser.setTenantAdminFlag(tenantUserVO.getTenantAdminFlag());
            //设置用户信息
            tenantUser.setUserId(tenantUserVO.getUserId());
            tenantUser.setMerchantCode(tenantUserVO.getMerchantCode());
            tenantUser.setNickName(tenantUserVO.getNickName());
            return tenantUser;
        } catch (Exception e) {
            log.error("TenantInfoAdvice error url:{}", request.getRequestURI(),e);
        }
        return null;
    }


    /**
     * 检查是否需要登录必备参数
     *
     * @param reqUri .
     * @return .
     */
    private boolean notLoginIntercept(String reqUri) {
        for (String path : notLoginInterceptPaths.split(",")) {
            if (reqUri.startsWith(path)) {
                return true;
            }
        }
        return false;
    }


}
