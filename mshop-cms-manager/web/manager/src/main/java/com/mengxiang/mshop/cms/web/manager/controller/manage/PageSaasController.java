package com.mengxiang.mshop.cms.web.manager.controller.manage;

import com.akucun.cms.aggregation.stub.feign.res.ConferenceConfigRes;
import com.akucun.cms.model.vo.hotSale.HotSaleConfigVO;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.model.enums.TemplateUseChannel;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.SellRuleGetBatchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.core.service.page.PageQueryService;
import com.mengxiang.mshop.cms.core.service.page.PageService;
import com.mengxiang.mshop.cms.core.service.page.TenantService;
import com.mengxiang.mshop.cms.core.service.page.mengxiang.PageAkcCmsValidateService;
import com.mengxiang.mshop.cms.core.service.page.mengxiang.PageMengXiangService;
import com.mengxiang.mshop.cms.core.service.promo.PromoActivityService;
import com.mengxiang.mshop.cms.service.facade.common.request.*;
import com.mengxiang.mshop.cms.service.facade.common.result.PageSelectResp;
import com.mengxiang.mshop.cms.service.facade.common.result.PointsPageConfigResp;
import com.mengxiang.mshop.cms.service.facade.common.result.PromotionActivityListResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.MerchantShopResp;
import com.mengxiang.promo.service.facade.response.PromotionCouponListResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @menu 运营-页面搭建
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "梦饷云-页面搭建", tags = {"梦饷云-页面搭建"})
public class PageSaasController {
    
    @Autowired
    private PageMengXiangService mengXiangService;

    @Autowired
    private PageQueryService pageQueryService;

    @Autowired
    private PageService pageService;

    @Autowired
    private PageAkcCmsValidateService pageAkcCmsValidateService;

    @Autowired
    private TenantService tenantService;
    @Autowired
    private PromoActivityService promoActivityService;


    @ApiOperation(value = "获取超品会场信息")
    @GetMapping(value = "/api/page/saas/hotSalecInfo")
    public Result<HotSaleConfigVO> queryHotSalecInfo(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestParam("hotSaleId") Integer hotSaleId) {
        if (null == tenantUser) {
            //未登录
            return Result.error("用户未登录");
        }
        if (Objects.isNull(hotSaleId)) {
            return Result.error("超品会场ID不能为空");
        }
        return Result.success(pageAkcCmsValidateService.queryHotSalecInfo(hotSaleId));
    }

    @ApiOperation(value = "获取普通会场信息")
    @GetMapping(value = "/api/page/saas/conferenceBaseInfo")
    public Result<ConferenceConfigRes> conferenceBaseInfoAgg(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestParam("conferenceId") Integer conferenceId) {
        if (null == tenantUser) {
            //未登录
            return Result.error("用户未登录");
        }
        if (Objects.isNull(conferenceId)) {
            return Result.error("普通会场ID不能为空");
        }
        return Result.success(pageAkcCmsValidateService.conferenceBaseInfoAgg(conferenceId));
    }
    
    
    @ApiOperation(value = "梦饷云页面预览")
    @GetMapping(value = "/api/page/saas/preview")
    public Result<String> preview(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestParam("pageCode") String pageCode, @RequestParam("version") String version) {
        if (null == tenantUser) {
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(tenantService.preview(tenantUser.getTenantId(), pageCode,version));
    }


    @ApiOperation("页面详情")
    @GetMapping("/api/page/saas/detail")
    public Result<PageBO> detail(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestParam("pageCode") String pageCode, @RequestParam("version") String version) {
        if(null==tenantUser){
            return Result.error("用户未登录");
        }
        return pageService.detail(pageCode,version);
    }

    @ApiOperation(value = "页面列表条件分页查询")
    @PostMapping(value = "/api/page/saas/pageSelect")
    public Result<Pagination<PageSelectResp>> pageSelect(@ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
                                                         @RequestBody PageSearchReq req) {
        if (null == tenantUser) {
            return Result.error("用户未登录");
        }
        req.setTenantId(String.valueOf(tenantUser.getTenantId()));
        req.setOwnerType(PageOwnerType.SAAS_TENANT.getOwnerType());
        return pageQueryService.pageSelect(req);
    }

    @ApiOperation(value = "页面保存/发布")
    @PostMapping(value = "/api/page/saas/save")
    public Result<PageBO> save(@ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser
            , @RequestBody SavePageRequest req) {
        if(null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }
        req.setTenantId(String.valueOf(tenantUser.getTenantId()));
        final String separator = ",";
        String channel = StringUtils.join(new String[]{
                TemplateUseChannel.APP.getChannel(),
                TemplateUseChannel.H5.getChannel(),
                TemplateUseChannel.MINIAPP.getChannel()
        }, separator);
        req.setChannel(channel);
        req.setCreateBy(StringUtils.isBlank(tenantUser.getNickName())?tenantUser.getUserName():tenantUser.getNickName());
        req.setCreateUserId(String.valueOf(tenantUser.getUserId()));
        req.setUpdateBy(StringUtils.isBlank(tenantUser.getNickName())?tenantUser.getUserName():tenantUser.getNickName());
        req.setOwnerId(String.valueOf(tenantUser.getTenantId()));
        req.setOwnerType(PageOwnerType.SAAS_TENANT.getOwnerType());
        req.setCreateUserCode(String.valueOf(tenantUser.getUserId()));
        return pageService.save(req);
    }

    @ApiOperation(value = "失效页面")
    @GetMapping(value = "/api/page/saas/setPageInvalidation")
    public Result<Boolean> setPageInvalidation(@ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser
            ,@RequestParam("pageCode")String pageCode, @RequestParam("version")String version) {
        return pageService.setPageInvalidation(pageCode, version,tenantUser.getUserName(),String.valueOf(tenantUser.getUserId()));
    }


    @ApiOperation(value = "恢复至上一发布版本")
    @GetMapping(value = "/api/page/saas/detailToBeforePublished")
    public Result<PageBO> detailToBeforePublished(@ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
                                                  @RequestParam("pageCode") String pageCode) {
        if(null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }
        return pageService.detailToBeforePublished(pageCode);
    }

    @ApiOperation(value = "复制并创建新页面")
    @GetMapping(value = "/api/page/saas/detailToNewPage")
    public Result<PageBO> detailToNewPage(@ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
                                          @RequestParam("pageCode") String pageCode,@RequestParam("version") String version) {
        if(null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }
        Result<PageBO> result = pageService.detailToNewPage(pageCode,version);
        if (Objects.nonNull(result) && result.isSuccess() && Objects.nonNull(result.getData())) {
            PageBO page = result.getData();
            //运营端 需要把复制的页面name 加上 复制
            String pageName = page.getName();
            page.setName(pageName + "_复制");
            return Result.success(page);
        }
        return result;
    }

    @ApiOperation(value = "批量获取规则信息")
    @PostMapping(value = "/api/page/saas/rules")
    public Result<List<PageRuleInfoResult>> getRuleBatch(@ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
                                                         @RequestBody SellRuleGetBatchRequest request) {
        if(null==tenantUser){
            //未登录
            return Result.error("用户未登录");
        }
        return pageService.getRuleBatch(request);
    }

    @ApiOperation(value = "查询会场3.0")
    @GetMapping(value = "/api/page/saas/pageDetailByCode")
    public Result<PageBO> pageDetailByCode(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestParam("pageCode") String pageCode) {
        if (null == tenantUser) {
            //未登录
            return Result.error("用户未登录");
        }
        return pageService.pageDetailByCode(pageCode);
    }

    @ApiOperation(value = "查询营销活动列表")
    @PostMapping("/api/page/saas/search/promoActivityList")
    public Result<Pagination<PromotionActivityListResp>> searchPromoActivityList(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestBody PromotionActivityReq req) {
        if (null == tenantUser) {
            //未登录
            return Result.error("用户未登录");
        }
        req.setPromoActivityVersion("NEW");
        req.setPromoActivityOwner("TENANT");
        req.setPromoActivityOwnerId(String.valueOf(tenantUser.getTenantId()));
        return Result.success(promoActivityService.searchPromoActivityList(req));
    }
    @ApiOperation(value = "查询优惠券列表")
    @PostMapping("/api/page/saas/search/promoCouponList")
    public Result<List<PromotionCouponListResp>> searchPromoCouponList(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestBody PromotionCouponReq req) {
        if (null == tenantUser) {
            //未登录
            return Result.error("用户未登录");
        }
        req.setPromoActivityVersion("NEW");
        req.setPromoActivityOwner("TENANT");
        return Result.success(promoActivityService.searchPromoCouponList(req));
    }

    @ApiOperation(value = "获取商家店铺信息")
    @GetMapping(value = "/api/page/saas/merchantShopInfo")
    public Result<MerchantShopResp> merchantShopInfo(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestParam("shopCode") String shopCode) {
        if (null == tenantUser) {
            return Result.error("用户未登录");
        }
        return Result.success(mengXiangService.getShopInfoByShopCode(shopCode));
    }

    @ApiOperation(value = "设置成主页")
    @GetMapping(value = "/api/page/saas/setPageIndex")
    public Result<Void> setPageIndex(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestParam("pageCode") String pageCode) {
        if (null == tenantUser) {
            return Result.error("用户未登录");
        }
        return pageService.setPageIndex(pageCode,String.valueOf(tenantUser.getTenantId()), PageType.SHOP_PAGE.getType(),PageOwnerType.SAAS_TENANT.getOwnerType());
    }
    @ApiOperation(value = "生成页面链接")
    @GetMapping(value = "/api/page/saas/generateUrl")
    public Result<Map<String,String>> generatePageUrl(
            @ApiIgnore @ModelAttribute(value = "tenantUser", binding = false) TenantUser tenantUser, @RequestParam("pageCode") String pageCode) {
        if (null == tenantUser) {
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(tenantService.generatePageUrl(pageCode,tenantUser.getTenantId()));
    }

    @ApiOperation(value = "查询积分会场相关配置")
    @GetMapping("/api/page/saas/search/pointsPageConfig")
    public Result<PointsPageConfigResp> searchPointsPageConfig(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser) {
        if (null == tenantUser) {
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(tenantService.searchPointsPageConfig(tenantUser));
    }

    @ApiOperation(value = "更新积分会场相关配置")
    @PostMapping("/api/page/saas/update/pointsPageConfig")
    public Result<Void> updatePointsPageConfig(
            @ApiIgnore @ModelAttribute(value = "tenantUser") TenantUser tenantUser,
            @RequestBody PointsPageConfigReq req) {
        if (null == tenantUser) {
            //未登录
            return Result.error("用户未登录");
        }
        tenantService.updatePointsPageConfig(tenantUser,req);
        return Result.success();
    }
}
