package com.mengxiang.mshop.cms.web.manager.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.mshop.cms.core.service.page.PageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2021/9/2 15:12
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = "AKC_MER_SHOP_TOPIC", consumerGroup = "AKC_MER_SHOP_MSHOP_CMS_MANAGER_GROUP", selectorExpression = "MER_SHOP_CONFIG_TAG")
public class CreateMerShopConsumer implements RocketMQListener<String> {


    @Autowired
    private PageService pageService;

    @Value("${page.CreateMerShopConsumer.flag:true}")
    private Boolean flag;

    @Override
    public void onMessage(String message) {
        log.info("CreateMerShopConsumer action msg:{}", message);
        if(Objects.isNull(message)){
            return;
        }
        if(flag){
            return;
        }
        try {
            JSONObject jsonObject = JSON.parseObject(message);
            if(Objects.isNull(jsonObject)){
                return;
            }
            if(StringUtils.isNotBlank(jsonObject.getString("shopCode"))){
                pageService.createDefaultPage(jsonObject.getString("shopCode"));
            }
        } catch (Exception e) {
            log.error("CreateMerShopConsumer error msg:{}", message, e);
            throw e;
        }
    }


}
