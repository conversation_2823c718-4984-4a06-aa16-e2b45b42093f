package com.mengxiang.mshop.cms.web.manager.controller;

import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.akucun.cms.aggregation.stub.feign.res.ConferenceConfigRes;
import com.akucun.cms.model.vo.hotSale.HotSaleConfigVO;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.enums.OperationLogActionEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.PageType;
import com.mengxiang.mshop.cms.core.model.enums.TemplateUseChannel;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.SellRuleGetBatchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.core.service.page.OperationLogService;
import com.mengxiang.mshop.cms.core.service.page.PageQueryService;
import com.mengxiang.mshop.cms.core.service.page.PageService;
import com.mengxiang.mshop.cms.core.service.page.mengxiang.PageAkcCmsValidateService;
import com.mengxiang.mshop.cms.core.service.page.mengxiang.PageMengXiangService;
import com.mengxiang.mshop.cms.service.facade.common.request.OperationLogSaveReq;
import com.mengxiang.mshop.cms.service.facade.common.request.PageSearchReq;
import com.mengxiang.mshop.cms.service.facade.common.request.TenantUser;
import com.mengxiang.mshop.cms.service.facade.common.result.PageSelectResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.MerchantShopResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.PageMengXiangPrewLink;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;
import com.x.live.center.dto.LiveInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "商户云-页面搭建服务", tags = {"商户云-页面搭建服务"})
public class PageController {
    
    @Autowired
    private PageQueryService pageQueryService;

    @Autowired
    private PageService pageService;

    @Value("${page.tenantId:151738493257170900}")
    private String tenantId;

    @Autowired
    private PageMengXiangService mengXiangService;

    @Autowired
    private PageAkcCmsValidateService pageAkcCmsValidateService;

    @Autowired
    private OperationLogService operationLogService;

    @ApiOperation("页面详情")
    @GetMapping("/api/page/manager/detail")
    public Result<PageBO> detail(
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
            @RequestParam("pageCode") String pageCode, @RequestParam("version") String version) {
        if(null==userInfo){
            return Result.error("用户未登录");
        }
        return pageService.detail(pageCode,version);
    }

    @ApiOperation(value = "页面列表条件分页查询")
    @PostMapping(value = "/api/page/manager/pageSelect")
    public Result<Pagination<PageSelectResp>> pageSelect(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
            @RequestBody PageSearchReq req) {
        if (null == userInfo) {
            return Result.error("用户未登录");
        }
//        req.setChannel(TemplateUseChannel.APP.getChannel());
        req.setTenantId(tenantId);
        req.setOwnerId(userInfo.getShopCode());
        req.setOwnerType(PageOwnerType.SUPPLIER.getOwnerType());
        if(StringUtils.isBlank(req.getType())){
            //商家默认查询 SHOP和 SHOPMICRO 类型
            List<String> typeList = Lists.newArrayList();
            typeList.add(PageType.SHOP_PAGE.getType());
            typeList.add(PageType.SHOP_MICRO_PAGE.getType());
            req.setTypeList(typeList);
        }
        return pageQueryService.pageSelect(req);
    }

    @ApiOperation(value = "页面保存/发布")
    @PostMapping(value = "/api/page/save")
    public Result<PageBO> save(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo, @RequestBody SavePageRequest req) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        req.setTenantId(tenantId);
        req.setChannel(TemplateUseChannel.APP.getChannel());
        req.setCreateBy(userInfo.getUsername());
        req.setCreateUserId(String.valueOf(userInfo.getUserId()));
        req.setUpdateBy(String.valueOf(userInfo.getUserId()));
        req.setOwnerId(userInfo.getShopCode());
        req.setOwnerType(PageOwnerType.SUPPLIER.getOwnerType());
        req.setShopName(userInfo.getShopName());
        return pageService.saveByMer(req,userInfo);
    }

    @ApiOperation(value = "失效页面")
    @GetMapping(value = "/api/page/manager/setPageInvalidation")
    public Result<Boolean> setPageInvalidation(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,@RequestParam("pageCode")String pageCode, @RequestParam("version")String version) {
        return pageService.setPageInvalidation(pageCode, version,userInfo.getUsername(),String.valueOf(userInfo.getUserId()));
    }

    @ApiOperation(value = "使用模版")
    @GetMapping(value = "/api/page/detailByTemplate")
    public Result<PageBO> detailByTemplate(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
                               @RequestParam("templateCode") String templateCode) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        return pageService.detailByTemplate(templateCode,"supplier","supplier");
    }


    @ApiOperation(value = "恢复至上一发布版本")
    @GetMapping(value = "/api/page/detailToBeforePublished")
    public Result<PageBO> detailToBeforePublished(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
                               @RequestParam("pageCode") String pageCode) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        return pageService.detailToBeforePublished(pageCode);
    }

    @ApiOperation(value = "页面预览")
    @GetMapping(value = "/api/page/preview")
    public Result<Map<String, PageMengXiangPrewLink>> preview(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
                                                              @RequestParam("pageCode") String pageCode,
                                                              @RequestParam(value = "role", required = false) String role,
                                                              @RequestParam(value = "version", required = false) String version, @RequestParam(value = "type", defaultValue = "1", required = false) Integer type) {
        if(null==userInfo){
            //未登录outer/merchant-shop/info?
            return Result.error("用户未登录");
        }
        if(StringUtils.isBlank(pageCode)){
            return Result.error("pageCode不能为空");
        }
        if(StringUtils.isBlank(version)){
            return Result.error("version不能为空");
        }
        if(Objects.equals(type,2)){
            //商家会场3.0
            return Result.success(mengXiangService.preview(null, pageCode,version));
        }else{
            //商家店铺装修
//            if(StringUtils.isBlank(role)){
//                return Result.error("role不能为空");
//            }
            return Result.success(pageService.previewShop(null, pageCode,version,userInfo.getShopCode()));

        }
    }

    @ApiOperation(value = "生成页面链接")
    @GetMapping(value = "/api/page/generateUrl")
    public Result<Map<String,String>> generatePageUrl(
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo, @RequestParam("pageCode") String pageCode) {
        if (null == userInfo) {
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(mengXiangService.generatePageUrl(pageCode));
    }

    @ApiOperation(value = "复制并创建新页面")
    @GetMapping(value = "/api/page/detailToNewPage")
    public Result<PageBO> detailToNewPage(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
                                                  @RequestParam("pageCode") String pageCode,@RequestParam("version") String version) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        Result<PageBO> result = pageService.detailToNewPage(pageCode,version);
        if (Objects.nonNull(result) && result.isSuccess() && Objects.nonNull(result.getData())) {
            PageBO page = result.getData();
            //运营端 需要把复制的页面name 加上 复制
            String pageName = page.getName();
            page.setName(pageName + "_复制");
            return Result.success(page);
        }
        return result;
    }

    @ApiOperation(value = "创建店铺默认页面")
    @GetMapping(value = "/api/page/createDefaultPage")
    public Result<PageBO> createDefaultPage(@RequestParam("shopCode") String shopCode,
                                          @RequestParam("mCode") String mCode,
                                          @RequestParam("operatorUserName") String operatorUserName) {
        return pageService.createDefaultPage(shopCode);
    }

    @ApiOperation(value = "强制更新模板1")
    @GetMapping(value = "/api/page/updateMerPage")
    public Result<PageBO> updateMerPage(@RequestParam("shopCode") String shopCode) {
        return pageService.updateMerPage(shopCode);
    }

    @ApiOperation(value = "批量获取规则信息")
    @PostMapping(value = "/api/page/rules")
    public Result<List<PageRuleInfoResult>> getRuleBatch(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
                                                         @RequestBody SellRuleGetBatchRequest request) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        return pageService.getRuleBatch(request);
    }

    @ApiOperation(value = "获取普通会场信息")
    @GetMapping(value = "/api/page/conferenceBaseInfo")
    public Result<ConferenceConfigRes> conferenceBaseInfoAgg(
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
            @RequestParam("conferenceId") Integer conferenceId) {
        if (null == userInfo) {
            //未登录
            return Result.error("用户未登录");
        }
        if (Objects.isNull(conferenceId)) {
            return Result.error("普通会场ID不能为空");
        }
        return Result.success(pageAkcCmsValidateService.conferenceBaseInfoAgg(conferenceId));
    }

    @ApiOperation(value = "获取超品会场信息")
    @GetMapping(value = "/api/page/hotSalecInfo")
    public Result<HotSaleConfigVO> queryHotSalecInfo(
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
            @RequestParam("hotSaleId") Integer hotSaleId) {
        if (null == userInfo) {
            //未登录
            return Result.error("用户未登录");
        }
        if (Objects.isNull(hotSaleId)) {
            return Result.error("超品会场ID不能为空");
        }
        return Result.success(pageAkcCmsValidateService.queryHotSalecInfo(hotSaleId));
    }

    @ApiOperation(value = "获取商家店铺信息")
    @GetMapping(value = "/api/page/merchantShopInfo")
    public Result<MerchantShopResp> merchantShopInfo(
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
            @RequestParam("shopCode") String shopCode) {
        if (null == userInfo) {
            return Result.error("用户未登录");
        }
        return Result.success(mengXiangService.getShopInfoByShopCode(shopCode));
    }

    @GetMapping(value = "/api/page/queryLiveBasics")
    @ApiOperation(value = "检查直播间id")
    public Result<LiveInfoDTO> queryLiveBasics(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
                                               @ApiParam(name = "liveRoomId", value = "直播间ID",required = true) @RequestParam String liveRoomId) {
        if (null == userInfo) {
            //未登录
            return Result.error("用户未登录");
        }
        if (StringUtils.isBlank(liveRoomId)) {
            return Result.error("直播间ID不能为空");
        }
        return Result.success(pageAkcCmsValidateService.queryLiveBasics(liveRoomId));
    }

    @ApiOperation(value = "保存正在编辑的日志")
    @GetMapping(value = "/api/page/operationLog/addByEDIT")
    public Result<Boolean> operationLogAddByEDIT(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo
            , @RequestParam("pageCode") String pageCode) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        OperationLogSaveReq request = new OperationLogSaveReq();
        request.setAction(OperationLogActionEnum.EDIT.getCode());
        request.setBizCode(pageCode);
        request.setBizType(1);
        request.setRemark(OperationLogActionEnum.EDIT.getDesc());
        request.setCreateBy(userInfo.getUsername());
        request.setCreateUserId(String.valueOf(userInfo.getUserId()));
        request.setOwnerType(PageOwnerType.SUPPLIER.getOwnerType());
        return operationLogService.saveOperationLog(request);
    }


    @ApiOperation(value = "设置成主页")
    @GetMapping(value = "/api/page/setPageIndex")
    public Result<Void> setPageIndex(
            @ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
            @RequestParam("pageCode") String pageCode) {
        if (null == userInfo) {
            return Result.error("用户未登录");
        }
        return pageService.setPageIndex(pageCode,userInfo.getShopCode(), PageType.SHOP_PAGE.getType(),PageOwnerType.SUPPLIER.getOwnerType());
    }
}
