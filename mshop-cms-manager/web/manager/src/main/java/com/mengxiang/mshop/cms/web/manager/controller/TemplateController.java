package com.mengxiang.mshop.cms.web.manager.controller;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.result.PageTemplateResult;
import com.mengxiang.mshop.cms.core.service.page.TemplateService;
import com.mengxiang.mshop.cms.service.facade.common.result.user.MerchantUserInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "页面搭建模板服务", tags = {"页面搭建模板服务"})
public class TemplateController {

    @Autowired
    private TemplateService templateService;


    @ApiOperation(value = "模版列表")
    @GetMapping(value = "/api/page/template/list")
    public Result<List<PageTemplateResult>> save(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo
            , @RequestParam("pageUseType") String pageUseType , @RequestParam(value = "version", required = false) Integer version) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(templateService.list("supplier","supplier",pageUseType,version));
    }

    @ApiOperation(value = "模版详情")
    @GetMapping(value = "/api/page/template/detail")
    public Result<PageTemplateResult> detail(@ApiIgnore @ModelAttribute(value = "merchantUserInfo", binding = false) MerchantUserInfo userInfo,
                                                  @RequestParam("templateCode") String templateCode) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(templateService.detail(templateCode));
    }
}
