package com.mengxiang.mshop.cms.web.manager.config;

import com.mengxiang.base.common.model.exception.BusinessException;
import com.mengxiang.base.common.model.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常捕捉
 * <AUTHOR>
 */
@ControllerAdvice
@Slf4j
@Order(1)
public class GlobalExceptionHandler {
    
    /**
     * 异常捕捉处理
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public Result errorHandler(Exception ex, HttpServletRequest request) {
        if (null != request) {
            StringBuilder strBuild = new StringBuilder();
            strBuild.append("global catch reqUrl:");
            strBuild.append(request.getRequestURI());
            if (HttpMethod.GET.matches(request.getMethod())) {
                strBuild.append(" queryString:");
                strBuild.append(request.getQueryString());
            }
            log.error(strBuild.toString(), ex);
            ex.printStackTrace();
            return Result.error("系统内部错误,请稍后重试");
        } else {
            log.error("global catch", ex);
            ex.printStackTrace();
            return Result.error("系统内部错误,请稍后重试");
        }
    }
    
    @ResponseBody
    @ExceptionHandler(IllegalArgumentException.class)
    public Result illegalArgumentException(IllegalArgumentException e) {
        Result fail = Result.error(e.getLocalizedMessage());
        return fail;
    }

    @ResponseBody
    @ExceptionHandler({BusinessException.class})
    public Result<Void> codeException(Exception e) {
        log.warn("codeException BusinessException", e);
        return Result.error(((BusinessException) e).getCode(), e.getMessage());
    }
}
