package com.mengxiang.mshop.cms.web.manager.controller.manage;

import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.akucun.cms.aggregation.stub.feign.res.ConferenceConfigRes;
import com.akucun.cms.model.vo.hotSale.HotSaleConfigVO;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.constant.CmsProdConstant;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.enums.OperationLogActionEnum;
import com.mengxiang.mshop.cms.core.model.enums.PageOwnerType;
import com.mengxiang.mshop.cms.core.model.enums.TemplateUseChannel;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.SellRuleGetBatchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.core.service.page.OperationLogService;
import com.mengxiang.mshop.cms.core.service.page.PageQueryService;
import com.mengxiang.mshop.cms.core.service.page.PageService;
import com.mengxiang.mshop.cms.core.service.page.mengxiang.PageAkcCmsValidateService;
import com.mengxiang.mshop.cms.core.service.page.mengxiang.PageMengXiangService;
import com.mengxiang.mshop.cms.service.facade.common.request.OperationLogSaveReq;
import com.mengxiang.mshop.cms.service.facade.common.request.PageSearchReq;
import com.mengxiang.mshop.cms.service.facade.common.result.PageSelectResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.MerchantShopResp;
import com.mengxiang.mshop.cms.service.facade.common.result.mengxiang.PageMengXiangPrewLink;
import com.x.live.center.dto.LiveInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @menu 运营-页面搭建
 */
@RestController
@RequestMapping(CmsProdConstant.OUTER_API_ROOT_URL)
@Api(value = "运营-页面搭建", tags = {"运营-页面搭建"})
public class PageMengXiangController {
    
    @Autowired
    private PageMengXiangService mengXiangService;

    @Autowired
    private PageQueryService pageQueryService;

    @Autowired
    private PageService pageService;

    @Autowired
    private PageAkcCmsValidateService pageAkcCmsValidateService;

    @Value("${page.tenantId:151738493257170900}")
    private String tenantId;

    @Autowired
    private OperationLogService operationLogService;

    @ApiOperation(value = "获取商家店铺信息")
    @GetMapping(value = "/api/page/mengxiang/merchantShopInfo")
    public Result<MerchantShopResp> merchantShopInfo(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestParam("shopCode") String shopCode) {
        if (null == userInfo) {
            return Result.error("用户未登录");
        }
        return Result.success(mengXiangService.getShopInfoByShopCode(shopCode));
    }

    @ApiOperation(value = "获取超品会场信息")
    @GetMapping(value = "/api/page/mengxiang/hotSalecInfo")
    public Result<HotSaleConfigVO> queryHotSalecInfo(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestParam("hotSaleId") Integer hotSaleId) {
        if (null == userInfo) {
            //未登录
            return Result.error("用户未登录");
        }
        if (Objects.isNull(hotSaleId)) {
            return Result.error("超品会场ID不能为空");
        }
        return Result.success(pageAkcCmsValidateService.queryHotSalecInfo(hotSaleId));
    }

    @ApiOperation(value = "获取普通会场信息")
    @GetMapping(value = "/api/page/mengxiang/conferenceBaseInfo")
    public Result<ConferenceConfigRes> conferenceBaseInfoAgg(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestParam("conferenceId") Integer conferenceId) {
        if (null == userInfo) {
            //未登录
            return Result.error("用户未登录");
        }
        if (Objects.isNull(conferenceId)) {
            return Result.error("普通会场ID不能为空");
        }
        return Result.success(pageAkcCmsValidateService.conferenceBaseInfoAgg(conferenceId));
    }
    @GetMapping(value = "/api/page/mengxiang/queryLiveBasics")
    @ApiOperation(value = "检查直播间id")
    public Result<LiveInfoDTO> queryLiveBasics(  @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @ApiParam(name = "liveRoomId", value = "直播间ID",required = true) @RequestParam String liveRoomId) {
        if (null == userInfo) {
            //未登录
            return Result.error("用户未登录");
        }
        if (StringUtils.isBlank(liveRoomId)) {
            return Result.error("直播间ID不能为空");
        }
        return Result.success(pageAkcCmsValidateService.queryLiveBasics(liveRoomId));
    }



    @ApiOperation(value = "梦饷页面预览")
    @GetMapping(value = "/api/page/mengxiang/preview")
    public Result<Map<String,PageMengXiangPrewLink>> preview(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestParam("pageCode") String pageCode, @RequestParam("version") String version) {
        String staffId = null;
        if (Objects.nonNull(userInfo)) {
            //未登录
            staffId = userInfo.getId();
        }
        return Result.success(mengXiangService.preview(staffId, pageCode,version));
    }
    
    @ApiOperation(value = "生成页面链接")
    @GetMapping(value = "/api/page/mengxiang/generateUrl")
    public Result<Map<String,String>> generatePageUrl(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo, @RequestParam("pageCode") String pageCode) {
        if (null == userInfo) {
            //未登录
            return Result.error("用户未登录");
        }
        return Result.success(mengXiangService.generatePageUrl(pageCode));
    }


    @ApiOperation("页面详情")
    @GetMapping("/api/page/mengxiang/detail")
    public Result<PageBO> detail(
            @ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
            @RequestParam("pageCode") String pageCode, @RequestParam("version") String version) {
        if(null==userInfo){
            return Result.error("用户未登录");
        }
        return pageService.detail(pageCode,version);
    }

    @ApiOperation(value = "页面列表条件分页查询")
    @PostMapping(value = "/api/page/mengxiang/pageSelect")
    public Result<Pagination<PageSelectResp>> pageSelect(@ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
                                                         @RequestBody PageSearchReq req) {
        if (null == userInfo) {
            return Result.error("用户未登录");
        }
        req.setTenantId(tenantId);
        return pageQueryService.pageSelectByMengXiang(req,userInfo);
    }

    @ApiOperation(value = "页面保存/发布")
    @PostMapping(value = "/api/page/mengxiang/save")
    public Result<PageBO> save(@ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo, @RequestBody SavePageRequest req) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        req.setTenantId(tenantId);

        return pageService.saveByMengXiang(req,userInfo);
    }

    @ApiOperation(value = "失效页面")
    @GetMapping(value = "/api/page/mengxiang/setPageInvalidation")
    public Result<Boolean> setPageInvalidation(@ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,@RequestParam("pageCode")String pageCode, @RequestParam("version")String version) {
        return pageService.setPageInvalidation(pageCode, version,userInfo.getRealname(),userInfo.getUsercode());
    }


    @ApiOperation(value = "恢复至上一发布版本")
    @GetMapping(value = "/api/page/mengxiang/detailToBeforePublished")
    public Result<PageBO> detailToBeforePublished(@ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
                                                  @RequestParam("pageCode") String pageCode) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        return pageService.detailToBeforePublished(pageCode);
    }

    @ApiOperation(value = "复制并创建新页面")
    @GetMapping(value = "/api/page/mengxiang/detailToNewPage")
    public Result<PageBO> detailToNewPage(@ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
                                          @RequestParam("pageCode") String pageCode,@RequestParam("version") String version) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        Result<PageBO> result = pageService.detailToNewPage(pageCode,version);
        if (Objects.nonNull(result) && result.isSuccess() && Objects.nonNull(result.getData())) {
            PageBO page = result.getData();
            //运营端 需要把复制的页面name 加上 复制
            String pageName = page.getName();
            page.setName(pageName + "_复制");
            return Result.success(page);
        }
        return result;
    }

    @ApiOperation(value = "批量获取规则信息")
    @PostMapping(value = "/api/page/mengxiang/rules")
    public Result<List<PageRuleInfoResult>> getRuleBatch(@ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo,
                                                         @RequestBody SellRuleGetBatchRequest request) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        return pageService.getRuleBatch(request);
    }

    @ApiOperation(value = "保存正在编辑的日志")
    @GetMapping(value = "/api/page/mengxiang/operationLog/addByEDIT")
    public Result<Boolean> operationLogAddByEDIT(@ApiIgnore @ModelAttribute(value = "manageUser", binding = false) UserInfo userInfo
            , @RequestParam("pageCode") String pageCode) {
        if(null==userInfo){
            //未登录
            return Result.error("用户未登录");
        }
        OperationLogSaveReq request = new OperationLogSaveReq();
        request.setAction(OperationLogActionEnum.EDIT.getCode());
        request.setBizCode(pageCode);
        request.setBizType(1);
        request.setRemark(OperationLogActionEnum.EDIT.getDesc());
        request.setCreateBy(userInfo.getRealname());
        request.setCreateUserId(userInfo.getUsercode());
        request.setOwnerType(PageOwnerType.MENGXIANG.getOwnerType());
        return operationLogService.saveOperationLog(request);
    }


    @ApiOperation(value = "查询分享文案")
    @GetMapping(value = "/api/page/findShareText")
    public Result<JSONObject> findShareText() {
        return Result.success(mengXiangService.findShareText());
    }

    @ApiOperation(value = "查询分享类目文案")
    @GetMapping(value = "/api/page/findShareTextByCategory")
    public Result<String> findShareTextByCategory(@RequestParam("category") String category) {
        return Result.success(mengXiangService.findShareTextByCategory(category));
    }

}
