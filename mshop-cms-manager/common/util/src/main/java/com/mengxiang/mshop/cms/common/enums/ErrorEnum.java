package com.mengxiang.mshop.cms.common.enums;


/**
 * <AUTHOR>
 */

public enum ErrorEnum {

    /**
     * 服务器内部错误
     */
    SERVER_ERROR(100000, "服务器内部错误"),
    /**
     * 系统调用错误
     */
    SYSTEM_CLIENT_ERROR(100001, "系统调用错误"),
    /**
     * 系统异常
     */
    SYSTEM_EXCEPTION(100002, "系统异常"),
    /**
     * 查询无数据
     */
    SYSTEM_NO_DATA_ERROR(100003, "查询无数据"),
   ;

    private int code;
    private String msg;

    ErrorEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
