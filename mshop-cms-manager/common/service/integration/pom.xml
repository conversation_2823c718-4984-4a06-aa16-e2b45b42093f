<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mengxiang.mshop.cms</groupId>
        <artifactId>mshop-cms-manager</artifactId>
        <version>1.0.2</version>
        <relativePath>../../../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mshop-cms-manager-common-service-integration</artifactId>
    <packaging>jar</packaging>

    <name>mshop-cms-manager-common-service-integration</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mengxiang.mshop.cms</groupId>
            <artifactId>mshop-cms-manager-common-util</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>common-rpc-scan</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    
        <dependency>
            <groupId>com.mengxiang.mshop.cms</groupId>
            <artifactId>mshop-cms-center-service-facade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.mshop.aggr</groupId>
            <artifactId>mshop-aggr-prod-service-facade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.product</groupId>
            <artifactId>akucun-base-product-facade-stub</artifactId>
            <version>2.1.35</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>activity-management-facade-stub</artifactId>
            <version>2.12.16</version>
            <exclusions>
                <exclusion>
                    <groupId>com.akucun.sp</groupId>
                    <artifactId>akucun-sp-stub</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.akucun.product</groupId>
                    <artifactId>akucun-base-product-facade-stub</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.github.openfeign.form</groupId>
                    <artifactId>feign-form-spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.github.openfeign.form</groupId>
                    <artifactId>feign-form</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mengxiang.base</groupId>
                    <artifactId>common-log</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.merchant.platform.query</groupId>
            <artifactId>merchant-platform-query-service-facade-token-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.akucun.merchant</groupId>
            <artifactId>merchant-platform-facade-stub</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>akucun-meroperationtool-management-facade-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aikucun.security.sso</groupId>
            <artifactId>security-sso-authentication</artifactId>
        </dependency>
    
        <dependency>
            <groupId>com.aikucun.security.sso</groupId>
            <artifactId>security-sso-permission</artifactId>
        </dependency>
    
<!--        <dependency>-->
<!--            <groupId>com.mengxiang.base.share</groupId>-->
<!--            <artifactId>user-share-prod-service-facade-common</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.mengxiang.base.share</groupId>
            <artifactId>user-share-prod-service-facade-common</artifactId>
            <version>1.0.114</version>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.promo</groupId>
            <artifactId>promo-service-facade</artifactId>
        </dependency>

        <!-- CMS-->
        <dependency>
            <groupId>com.akucun.cms</groupId>
            <artifactId>cms-aggregation-stub</artifactId>
            <version>1.6.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 直播 -->
        <dependency>
            <groupId>com.x.live</groupId>
            <artifactId>live-center-stub</artifactId>
            <version>1.3.4</version>
        </dependency>

        <dependency>
            <groupId>com.akucun.sp</groupId>
            <artifactId>akucun-sp-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.sp</groupId>
            <artifactId>akucun-sp-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.merchant.platform.query</groupId>
            <artifactId>merchant-platform-query-service-facade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.mshop.gateway</groupId>
            <artifactId>wx-gateway-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.saas</groupId>
            <artifactId>tenant-core-service-facade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.base</groupId>
            <artifactId>base-short-url-stub</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

