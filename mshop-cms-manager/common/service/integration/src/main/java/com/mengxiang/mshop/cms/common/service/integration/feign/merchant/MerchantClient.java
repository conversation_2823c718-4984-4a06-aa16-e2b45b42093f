package com.mengxiang.mshop.cms.common.service.integration.feign.merchant;

import com.akucun.merchant.platform.model.PageRes;
import com.akucun.merchant.platform.model.dto.req.shop.PageByUserCodeReq;
import com.akucun.merchant.platform.model.vo.shop.MerShopVO;
import com.akucun.merchant.platform.remote.merchant.shop.IMerShopRemote;
import com.akucun.meroperationtool.facade.stub.api.ApiAmbUserFeign;
import com.akucun.meroperationtool.model.dto.AmbUserDTO;
import com.akucun.meroperationtool.model.dto.AmbUserDetailDTO;
import com.akucun.meroperationtool.model.dto.AmbUserSimpleDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.merchant.platform.query.service.facade.common.feign.MerFacade;
import com.mengxiang.merchant.platform.query.service.facade.common.feign.ShopFacade;
import com.mengxiang.merchant.platform.query.service.facade.common.request.QueryMerRequest;
import com.mengxiang.merchant.platform.query.service.facade.common.request.QueryShopRequest;
import com.mengxiang.merchant.platform.query.service.facade.common.result.QueryMerResult;
import com.mengxiang.merchant.platform.query.service.facade.common.result.QueryShopResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商户登录信息
 * <AUTHOR>
 */
@Component
@Slf4j
public class MerchantClient {

    @Autowired
    private ShopFacade shopFacade;

    @Autowired
    private MerFacade merFacade;
    @Autowired
    private IMerShopRemote merShopRemote;
    @Autowired
    private ApiAmbUserFeign apiAmbUserFeign;


    public QueryShopResult getShopInfoByShopCode(String shopCode) {
        QueryShopRequest request = new QueryShopRequest();
        request.setShopCode(shopCode);
        try {
            Result<QueryShopResult> result = shopFacade.getShopInfoByShopCode(request);
            if (result != null && result.isSuccess() && result.getData() != null) {
                return result.getData();
            } else {
                log.warn("shopFacade.getShopInfoWithBrandListByShopCode warn, shopCode:{}, res:{}", shopCode, JSONObject.toJSONString(result));
            }
        } catch (Throwable e) {
            log.error("shopFacade.getShopInfoWithBrandListByShopCode error, shopCode:{}, res:{}", shopCode, e);
        }
        return null;
    }

    /**
     * 根据店铺code查询店铺信息--批量
     * @param shopCodeList
     * @return
     */
    public Map<String,QueryShopResult>  getShopInfoListByShopCodeList(List<String> shopCodeList) {
        Map<String,QueryShopResult> map = Maps.newHashMap();
        if(CollectionUtils.isEmpty(shopCodeList)){
            return map;
        }
        try {
            List<QueryShopRequest> req = shopCodeList.stream().map(x->{
                QueryShopRequest queryShopRequest = new QueryShopRequest();
                queryShopRequest.setShopCode(x);
                return queryShopRequest;
            }).collect(Collectors.toList());
            Result<List<QueryShopResult>> result = shopFacade.getShopInfoListByShopCodeList(req);
            if (null == result || !result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
                log.warn("MerchantClient getShopInfoListByShopCodeList 根据店铺code查询店铺信息--批量 req:{},resp:{}", JSON.toJSONString(shopCodeList), JSON.toJSONString(result));
                return null;
            }
            return result.getData().stream().collect(Collectors.toMap(QueryShopResult::getShopCode, Function.identity(),(e1, e2)->e1));
        } catch (Exception e) {
            log.error("MerchantClient getShopInfoListByShopCodeList 根据店铺code查询店铺信息--批量 异常 req:{}", JSON.toJSONString(shopCodeList));
        }
        return map;
    }

    /**
     * 根据商家id查询商家信息
     * @param merchantId
     * @return
     */
    public QueryMerResult getMerInfoByMerId(Long merchantId) {
        try {
            QueryMerRequest queryMerRequest = new QueryMerRequest();
            queryMerRequest.setMerId(merchantId);
            Result<QueryMerResult> result = merFacade.getMerInfoByMerId(queryMerRequest);
            if (null == result || !result.isSuccess()) {
                log.warn("MerchantClient getMerInfoByMerId 根据商家id查询商家信息 req:{},resp:{}", JSON.toJSONString(merchantId), JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("MerchantClient getMerInfoByMerId 根据商家id查询商家信息 异常 req:{}", JSON.toJSONString(merchantId));
        }
        return null;
    }
    /**
     * 根据商家code查询商家信息
     * @param merchantCode
     * @merchantCode
     */
    public QueryMerResult getMerInfoByMerCode(String merchantCode) {
        try {
            QueryMerRequest queryMerRequest = new QueryMerRequest();
            queryMerRequest.setMerCode(merchantCode);
            Result<QueryMerResult> result = merFacade.getMerInfoByMerCode(queryMerRequest);
            if (null == result || !result.isSuccess()) {
                log.warn("MerchantClient getMerInfoByMerCode 根据商家code查询商家信息 req:{},resp:{}", JSON.toJSONString(merchantCode), JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("MerchantClient getMerInfoByMerCode 根据商家code查询商家信息 异常 req:{}", JSON.toJSONString(merchantCode));
        }
        return null;
    }

    /**
     * 查询店铺BD+主营类目
     * @param merchantCode
     * @merchantCode
     */
    public MerShopVO queryByShopCode(String shopCode) {
        try {
            com.akucun.common.Result<MerShopVO> result = merShopRemote.queryByShopCode(shopCode);
            if (null == result || !result.isSuccess()) {
                log.warn("MerchantClient getMerInfoByMerCode 根据商家code查询商家信息 req:{},resp:{}", JSON.toJSONString(shopCode), JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("MerchantClient getMerInfoByMerCode 根据商家code查询商家信息 异常 req:{}", JSON.toJSONString(shopCode),e);
        }
        return null;
    }


    /**
     * 店铺列表查询接口
     * @param merchantCode
     * @merchantCode
     */
    public PageRes<MerShopVO> pageByUserCode(PageByUserCodeReq req) {
        try {
            com.akucun.common.Result<PageRes<MerShopVO>> result = merShopRemote.pageByUserCode(req);
            if (null == result || !result.isSuccess()) {
                log.warn("MerchantClient pageByUserCode 店铺列表查询接口 req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("MerchantClient pageByUserCode 店铺列表查询接口 异常 req:{}", JSON.toJSONString(req),e);
        }
        return null;
    }


    /**
     * 查询工号
     * @param userCodeList
     * @param matchGpmUserId
     * @merchantCode
     */
    public List<AmbUserDTO> listAmbUser(List<String> userCodeList,Boolean matchGpmUserId) {
        try {
            com.akucun.common.Result<List<AmbUserDTO>> result = apiAmbUserFeign.listAmbUser(userCodeList,matchGpmUserId);
            if (null == result || !result.isSuccess()) {
                log.warn("MerchantClient listAmbUser 查询工号 req:{},{},resp:{}", JSON.toJSONString(userCodeList),matchGpmUserId, JSON.toJSONString(result));
                return Lists.newArrayList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("MerchantClient listAmbUser 查询工号 异常 req:{},{}", JSON.toJSONString(userCodeList),matchGpmUserId,e);
        }
        return Lists.newArrayList();
    }

    /**
     * 查询类目长列表
     * @param categoryCode
     * @param businessType
     * @param sceneType
     * @merchantCode
     */
    public List<AmbUserSimpleDTO> listCategoryLeader(String categoryCode,Integer businessType,Integer sceneType) {
        try {
            com.akucun.common.Result<List<AmbUserSimpleDTO>> result = apiAmbUserFeign.listCategoryLeader(categoryCode,businessType,sceneType);
            if (null == result || !result.isSuccess()) {
                log.warn("MerchantClient listCategoryLeader 查询类目长列表 req:{},{},{},resp:{}", categoryCode,businessType,sceneType, JSON.toJSONString(result));
                return Lists.newArrayList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("MerchantClient listCategoryLeader 查询类目长列表 异常 req:{},{},{}", categoryCode,businessType,sceneType,e);
        }
        return Lists.newArrayList();
    }

    /**
     * 查询管理员列表
     * @merchantCode
     */
    public List<AmbUserSimpleDTO> listAdmin() {
        try {
            com.akucun.common.Result<List<AmbUserSimpleDTO>> result = apiAmbUserFeign.listAdmin();
            if (null == result || !result.isSuccess()) {
                log.warn("MerchantClient listCategoryLeader  查询管理员列表 resp:{}", JSON.toJSONString(result));
                return Lists.newArrayList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("MerchantClient listCategoryLeader 查询管理员列表 异常 ",e);
        }
        return Lists.newArrayList();
    }

    /**
     * 查询用户身份
     * @merchantCode
     */
    public AmbUserDetailDTO detailAmbUser(String userCode, Boolean matchGpmUserId) {
        try {
            com.akucun.common.Result<AmbUserDetailDTO> result = apiAmbUserFeign.detailAmbUser(userCode,matchGpmUserId);
            if (null == result || !result.isSuccess()) {
                log.warn("MerchantClient listCategoryLeader  查询管理员列表 resp:{}", JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("MerchantClient listCategoryLeader 查询管理员列表 异常 ",e);
        }
        return null;
    }
}
