package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.aikucun.common2.base.exception.BusinessException;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.promo.service.facade.feign.coupon.PromotionFacade;
import com.mengxiang.promo.service.facade.request.PromotionCouponActivityListRequest;
import com.mengxiang.promo.service.facade.request.PromotionCouponInfoRequest;
import com.mengxiang.promo.service.facade.request.PromotionCouponListRequest;
import com.mengxiang.promo.service.facade.response.PromotionCouponActivityListResp;
import com.mengxiang.promo.service.facade.response.PromotionCouponInfoResp;
import com.mengxiang.promo.service.facade.response.PromotionCouponListResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PromoActivityClient {

    @Autowired
    private PromotionFacade promotionFacade;

    /**
     * 优惠券营销活动列表（新老兼容）
     * @param req
     * @return
     */
    public Pagination<PromotionCouponActivityListResp> promoActivityList(PromotionCouponActivityListRequest req) {
        try {
            Result<Pagination<PromotionCouponActivityListResp>> result = promotionFacade.promoActivityList(req);
            if (null == result || !result.isSuccess()) {
                log.warn("PromoActivityClient promoActivityList 优惠券营销活动列表（新老兼容） req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
                if(Objects.nonNull(result)){
                    throw new BusinessException(result.getMessage());
                }
                return null;
            }
            log.warn("PromoActivityClient promoActivityList 优惠券营销活动列表（新老兼容） req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return result.getData();
        } catch (Exception e) {
            log.error("PromoActivityClient promoActivityList 优惠券营销活动列表（新老兼容） 异常 req:{}", JSON.toJSONString(req),e);
        }
        return null;
    }


    /**
     * 优惠券营销活动列表（新老兼容）
     * @param req
     * @return
     */
    public List<PromotionCouponListResp> promoCouponList(PromotionCouponListRequest req) {
        try {
            Result<List<PromotionCouponListResp>> result = promotionFacade.promoCouponList(req);
            if (null == result || !result.isSuccess()) {
                log.warn("PromoActivityClient promoCouponList 优惠券列表（新老兼容） req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
                if(Objects.nonNull(result)){
                    throw new BusinessException(result.getMessage());
                }
                return Lists.newArrayList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("PromoActivityClient promoCouponList 优惠券列表（新老兼容） 异常 req:{}", JSON.toJSONString(req),e);
        }
        return Lists.newArrayList();
    }
    /**
     * 优惠券营销活动列表（新老兼容）
     * @param req
     * @return
     */
    public List<PromotionCouponInfoResp> promoCouponListById(List<PromotionCouponInfoRequest> couponIdList,Boolean pointsCoupon) {
        try {
            Result<List<PromotionCouponInfoResp>> result = promotionFacade.promoCouponInfoList(couponIdList,pointsCoupon);
            if (null == result || !result.isSuccess()) {
                log.warn("PromoActivityClient promoCouponInfoList 优惠券列表（新老兼容） req:{},resp:{}", JSON.toJSONString(couponIdList), JSON.toJSONString(result));
                if(Objects.nonNull(result)){
                    throw new BusinessException(result.getMessage());
                }
                return Lists.newArrayList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("PromoActivityClient promoCouponInfoList 优惠券列表（新老兼容） 异常 req:{}", JSON.toJSONString(couponIdList),e);
        }
        return Lists.newArrayList();
    }
    /**
     * 优惠券选择校验
     * @param req
     * @return
     */
    public Boolean promoCouponCheck(String promoActivityId,String promoActivityRange) {
        try {
            Result<Boolean>  result = promotionFacade.promoCouponCheck(promoActivityId,promoActivityRange);
            if (null == result || !result.isSuccess()) {
                log.warn("PromoActivityClient promoCouponCheck 优惠券选择校验，新营销使用 req:{}{},resp:{}", promoActivityId,promoActivityRange, JSON.toJSONString(result));
                if(Objects.nonNull(result)){
                    throw new BusinessException(result.getMessage());
                }
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("PromoActivityClient promoCouponCheck 优惠券选择校验，新营销使用 异常 req:{}{}", promoActivityId,promoActivityRange,e);
        }
        return null;
    }


}