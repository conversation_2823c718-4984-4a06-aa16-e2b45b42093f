package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.result.PageTemplateResult;
import com.mengxiang.mshop.cms.service.facade.common.feign.template.TemplateFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class TemplateClient {

    @Autowired
    private TemplateFeign templateFeign;

    /**
     * 模版列表
     * @param ownerId
     * @param ownerType
     * @return
     */
    public List<PageTemplateResult> list(String ownerId,String ownerType,String pageUseType) {

        try {
            Result<List<PageTemplateResult>> result = templateFeign.list(ownerId,ownerType,pageUseType);
            if (null == result || !result.isSuccess()) {
                log.warn("TemplateClient list 模版列表 req:{},{},resp:{}", ownerId,ownerType, JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("TemplateClient list 模版列表  req:{},{}",  ownerId,ownerType);
        }
        return Lists.newArrayList();
    }

    /**
     * 页面模版详情
     * @param templateCode
     * @return
     */
    public PageTemplateResult detail(String templateCode) {

        try {
            Result<PageTemplateResult> result = templateFeign.detail(templateCode);
            if (null == result || !result.isSuccess()) {
                log.warn("TemplateClient detail 页面模版详情 req:{},resp:{}", templateCode, JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("TemplateClient detail 页面模版详情  req:{}",templateCode);
        }
        return null;
    }
}
