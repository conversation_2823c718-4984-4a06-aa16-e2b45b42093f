package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.domain.resource.NavigationResourceBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.PlatPageBO;
import com.mengxiang.mshop.cms.service.facade.common.feign.resource.ResourceManagerFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class MshopCmsClient {
    @Autowired
    private ResourceManagerFeign resourceManagerFeign;

    public NavigationResourceBO getPlatPageDataByTenantInfo(NavigationResourceBO req){
        try {
            Result<NavigationResourceBO> result = resourceManagerFeign.getPlatPageDataByTenantInfo(req);
            if (result.isSuccess() && ObjectUtils.isNotEmpty(result.getData())){
                log.info("getPlatPageDataBySourceType result:{}", result);
                return result.getData();
            }
        } catch (Exception e) {
            log.error("getPlatPageDataBySourceType error:{}", e);
        }
        return null;
    }
    public boolean savePlatPageData(NavigationResourceBO req){
        try {
            Result<Void> booleanResult = resourceManagerFeign.savePlatPageData(req);
            if(ObjectUtils.isEmpty(booleanResult) || !booleanResult.isSuccess()){
                log.info("savePlatPageData result:{}", booleanResult.getData());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("savePlatPageData error:{}", e);
            return false;
        }
    }
}
