package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.enums.ErrorEnum;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.SellRuleGetBatchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;
import com.mengxiang.mshop.cms.core.model.result.SafeModelRuleLabelResult;
import com.mengxiang.mshop.cms.service.facade.common.feign.page.PageFeign;
import com.mengxiang.mshop.cms.service.facade.common.feign.page.PageManagerFeign;
import com.mengxiang.mshop.cms.service.facade.common.feign.rule.RuleFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PageClient {

    @Autowired
    private PageManagerFeign pageManagerFeign;

    @Autowired
    private RuleFeign ruleFeign;

    @Resource
    private PageFeign pageFeign;

    public Result<List<PageRuleInfoResult>> getRuleBatch(SellRuleGetBatchRequest request) {
        Result<List<PageRuleInfoResult>> result = ruleFeign.rules(request);
        if (Objects.isNull(result)) {
            log.warn("ruleFeign rules result is null, ruleIds:{}", JSON.toJSONString(request));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("ruleFeign rules result is fail, ruleIds:{},resp:{}", JSON.toJSONString(request), JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("ruleFeign rules data is null, ruleIds:{},resp:{}", JSON.toJSONString(request), JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }

    public Result<PageBO> save(SavePageRequest req) {
        Result<PageBO> result = pageManagerFeign.save(req);
        if (Objects.isNull(result)) {
            log.warn("PageClient save result is null, req:{}", JSON.toJSONString(req));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("PageClient save result is fail, req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("PageClient save data is null, req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }

    public Result<Boolean> setPageInvalidation(String pageCode,String version,String updateBy, String updateUserId) {
        Result<Boolean> result = pageManagerFeign.setPageInvalidation(pageCode,version,updateBy,updateUserId);
        if (Objects.isNull(result)) {
            log.warn("PageClient setPageInvalidation result is null, pageCode:{} version:{}", pageCode,version);
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("PageClient setPageInvalidation result is fail, pageCode:{} version:{} resp:{}",  pageCode,version, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("PageClient setPageInvalidation data is null, pageCode:{} version:{} resp:{}", pageCode, version, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }

    public Result<PageBO> detail(String pageCode,String version) {
        Result<PageBO> result = pageManagerFeign.detail(pageCode,version);
        if (Objects.isNull(result)) {
            log.warn("PageClient detail result is null, pageCode:{} version:{}", pageCode,version);
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("PageClient detail result is fail, pageCode:{} version:{} resp:{}", pageCode,version, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("PageClient detail data is null, pageCode:{} version:{} resp:{}",pageCode,version,  JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }

    public Result<PageBO> detailByTemplate(String templateCode,String ownerId, String ownerType) {
        Result<PageBO> result = pageManagerFeign.detailByTemplate(templateCode,ownerId,ownerType);
        if (Objects.isNull(result)) {
            log.warn("PageClient detailByTemplate result is null, templateCode:{}", templateCode);
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("PageClient detailByTemplate result is fail, templateCode:{} resp:{}", templateCode, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("PageClient detailByTemplate data is null, templateCode:{} resp:{}",templateCode,  JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }
    public Result<PageBO> detailToBeforePublished(String pageCode) {
        Result<PageBO> result = pageManagerFeign.detailToBeforePublished(pageCode);
        if (Objects.isNull(result)) {
            log.warn("PageClient detailToBeforePublished result is null, pageCode:{}", pageCode);
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("PageClient detailToBeforePublished result is fail, pageCode:{} resp:{}", pageCode, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("PageClient detailToBeforePublished data is null, pageCode:{} resp:{}",pageCode,  JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }

    public Result<String> preview(String ownerType,String pageCode,String role) {
        Result<String> result = pageManagerFeign.preview(ownerType,pageCode,role);
        if (Objects.isNull(result)) {
            log.warn("PageClient preview result is null, ownerType:{} pageCode:{} role:{}", ownerType, pageCode,role);
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("PageClient preview result is fail, ownerType:{} pageCode:{} role:{} resp:{}", ownerType, pageCode,role, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("PageClient preview data is null, ownerType:{} pageCode:{} role:{} resp:{}", ownerType, pageCode,role, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }

    public Result<PageBO> detailToNewPage(String pageCode,String version) {
        Result<PageBO> result = pageManagerFeign.detailToNewPage(pageCode,version);
        if (Objects.isNull(result)) {
            log.warn("PageClient detailToNewPage result is null, pageCode:{} version:{}", pageCode,version);
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("PageClient detailToNewPage result is fail, pageCode:{} version:{} resp:{}", pageCode,version, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("PageClient detailToNewPage data is null, pageCode:{} version:{} resp:{}",pageCode,version,  JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }
    public Result<Pagination<PageSelectResult>> pageSelect(PageSearchRequest request) {
        Result<Pagination<PageSelectResult>> result = pageManagerFeign.pageSelect(request);
        if (Objects.isNull(result)) {
            log.warn("PageClient pageSelect result is null, request:{}", JSONObject.toJSONString(request));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("PageClient pageSelect result is fail, request:{} resp:{}", JSONObject.toJSONString(request), JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("PageClient pageSelect data is null, request:{} resp:{}",JSONObject.toJSONString(request),JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }

    public Result<PageBO> detail(String pageCode) {
        Result<PageBO> result = pageFeign.detail(pageCode);
        if (Objects.isNull(result)) {
            log.warn("PageClient detail result is null, req:{}", pageCode);
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("PageClient detail result is fail, req:{},resp:{}", pageCode, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("PageClient detail data is null, req:{},resp:{}", pageCode, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }

    public Result<Void> setPageIndex(String pageCode, String ownerId,String pageType, String ownerType) {
        Result<Void> result = pageManagerFeign.setPageIndex(pageCode,ownerId,pageType,ownerType);
        if (Objects.isNull(result)) {
            log.warn("PageClient setPageIndex result is null, pageCode:{} version:{}", pageCode,ownerId);
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("PageClient setPageIndex result is fail, pageCode:{} version:{} resp:{}", pageCode,ownerId, JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        return result;
    }

    public List<SafeModelRuleLabelResult> findSafeModelRuleLabel() {
        Result<List<SafeModelRuleLabelResult>> result = ruleFeign.findSafeModelRuleLabel();
        if (Objects.isNull(result)) {
            log.warn("PageClient findSafeModelRuleLabel result is null");
            return Lists.newArrayList();
        }
        if (!result.isSuccess()) {
            log.warn("PageClient findSafeModelRuleLabel result is fail");
            return Lists.newArrayList();
        }
        return result.getData();
    }


    public PageBO detailByType(String ownerId,String pageType) {
        Result<PageBO> result = pageFeign.detailByType(ownerId,pageType);
        if (Objects.isNull(result)) {
            log.warn("PageClient detailByType result is null");
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("PageClient detailByType result is fail");
            return null;
        }
        return result.getData();
    }
}
