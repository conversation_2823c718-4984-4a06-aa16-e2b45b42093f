package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.enums.ErrorEnum;
import com.mengxiang.mshop.cms.core.model.request.workflow.WorkflowInfoRequest;
import com.mengxiang.mshop.cms.core.model.request.workflow.WorkflowInfoResp;
import com.mengxiang.mshop.cms.service.facade.common.feign.page.WorkbenchFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class WorkbenchClient {

    @Autowired
    private WorkbenchFeign workbenchFeign;

    public Result<WorkflowInfoResp> findWorkflowInfo(WorkflowInfoRequest req){

        Result<WorkflowInfoResp> result = workbenchFeign.findWorkflowInfo(req);

        if (Objects.isNull(result)) {
            log.warn("WorkbenchClient findWorkflowInfo result is null, req:{}", JSON.toJSONString(req));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", ErrorEnum.SYSTEM_CLIENT_ERROR.getMsg());
        }
        if (!result.isSuccess()) {
            log.warn("WorkbenchClient findWorkflowInfo result is fail, req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_CLIENT_ERROR.getCode() + "", result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.warn("WorkbenchClient findWorkflowInfo data is null, req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return Result.error(ErrorEnum.SYSTEM_NO_DATA_ERROR.getCode() + "",ErrorEnum.SYSTEM_NO_DATA_ERROR.getMsg());
        }
        return result;
    }


}
