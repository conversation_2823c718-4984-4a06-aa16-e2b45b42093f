package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.akucun.base.shorturl.stub.GenerateUrlVO;
import com.akucun.base.shorturl.stub.ShortUrlFacade;
import com.akucun.base.shorturl.stub.StorageDTO;
import com.akucun.mshop.gateway.wx.stub.others.dto.req.WeChatUnlimitShortUrlRequest;
import com.akucun.mshop.gateway.wx.stub.remote.mssagemanger.IWeChatGatewayService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.aikucun.common2.base.Result;

import java.util.Objects;

/**
 * 微信信息
 */
@Service
@Slf4j
public class WeChantFeignClient {

    @Autowired
    IWeChatGatewayService iWeChatGatewayService;

    @Autowired
    ShortUrlFacade shortUrlFacade;

    public String genUnLimitShortLinkPic(WeChatUnlimitShortUrlRequest req) {
        Result<String> result = iWeChatGatewayService.genUnLimitShortLinkPic(req);
        if (Objects.isNull(result)) {
            log.warn("WeChantFeignClient genUnLimitShortLinkPic result is null, req:{}", JSON.toJSONString(req));
            return null;
        }
        if (!result.getSuccess()) {
            log.warn("WeChantFeignClient genUnLimitShortLinkPic result is fail, req:{} resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return null;
        }
        if (Objects.isNull(result.getData())) {
            log.warn("WeChantFeignClient genUnLimitShortLinkPic data is null, req:{} resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return null;
        }
        return result.getData();
    }

    public GenerateUrlVO genShortV2(StorageDTO req) {
        Result<GenerateUrlVO> result = shortUrlFacade.genShortV2(req);
        if (Objects.isNull(result)) {
            log.warn("WeChantFeignClient genShortV2 result is null, req:{}", JSON.toJSONString(req));
            return null;
        }
        if (!result.getSuccess()) {
            log.warn("WeChantFeignClient genShortV2 result is fail, req:{} resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return null;
        }
        if (Objects.isNull(result.getData())) {
            log.warn("WeChantFeignClient genShortV2 data is null, req:{} resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return null;
        }
        log.info("WeChantFeignClient genShortV2 result, req:{}, res:{}", JSON.toJSONString(req), JSON.toJSONString(result));
        return result.getData();
    }
}
