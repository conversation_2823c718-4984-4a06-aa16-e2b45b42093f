package com.mengxiang.mshop.cms.common.service.integration.feign.security;

import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.core.model.request.content.ContentCheckRequest;
import com.mengxiang.mshop.cms.core.model.result.content.ContentCheckResponse;
import com.mengxiang.mshop.cms.service.facade.common.feign.security.ContentCheckFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 内容检测
 */
@Component
@Slf4j
public class ContentCheckClient {
    
    @Autowired
    private ContentCheckFeign contentCheckFeign;
    
    public List<ContentCheckResponse> contextTextCheck(ContentCheckRequest req) {
        try {
            Result<List<ContentCheckResponse>> result = contentCheckFeign.contextTextCheck(req);
            if (null == result || !result.isSuccess()) {
                log.error("[[contextTextCheck]] 调用内容检测 失败 req:{},resp:{}", JSON.toJSONString(req),JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("[[contextTextCheck]] 调用内容检测 异常 req:{}", JSON.toJSONString(req),e);
        }
        return null;
    }
}
