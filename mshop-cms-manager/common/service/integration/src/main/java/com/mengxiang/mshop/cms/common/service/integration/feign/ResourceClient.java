package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.cms.common.enums.ErrorEnum;
import com.mengxiang.mshop.cms.core.model.domain.base.PageBO;
import com.mengxiang.mshop.cms.core.model.domain.resource.NavigationResourceBO;
import com.mengxiang.mshop.cms.core.model.request.PageSearchRequest;
import com.mengxiang.mshop.cms.core.model.request.SavePageRequest;
import com.mengxiang.mshop.cms.core.model.request.SellRuleGetBatchRequest;
import com.mengxiang.mshop.cms.core.model.result.PageRuleInfoResult;
import com.mengxiang.mshop.cms.core.model.result.PageSelectResult;
import com.mengxiang.mshop.cms.core.model.result.SafeModelRuleLabelResult;
import com.mengxiang.mshop.cms.service.facade.common.feign.resource.ResourceManagerFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ResourceClient {

    @Autowired
    private ResourceManagerFeign resourceManagerFeign;


    public Result saveNavigationResource(NavigationResourceBO request) {
        Result<Void> result = resourceManagerFeign.saveNavigationResource(request);
        if (Objects.isNull(result)) {
            log.warn("resourceManagerFeign saveNavigationResource result is null, req:{}", JSON.toJSONString(request));
        }
        if (!result.isSuccess()) {
            log.warn("resourceManagerFeign saveNavigationResource result is fail, req:{},resp:{}", JSON.toJSONString(request), JSON.toJSONString(result));
        }
        return result;
    }

    public NavigationResourceBO getNavigationResourceById(NavigationResourceBO req) {
        Result<NavigationResourceBO> result = resourceManagerFeign.getNavigationResourceById(req);
        if (Objects.isNull(result)) {
            log.warn("resourceManagerFeign getNavigationResourceById result is null, req:{}", JSON.toJSONString(req));
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("resourceManagerFeign getNavigationResourceById result is fail, req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return null;
        }
        if (Objects.isNull(result.getData())) {
            log.warn("resourceManagerFeign getNavigationResourceById data is null, req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            return null;
        }
        return result.getData();
    }

}
