package com.mengxiang.mshop.cms.common.service.integration.feign.share;

import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.base.share.service.facade.common.feign.ShortCodeServiceFacade;
import com.mengxiang.base.share.service.facade.common.request.GenerateShortCodeLinkRequest;
import com.mengxiang.base.share.service.facade.common.result.GenerateShortCodeLinkVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class ShortCodeServiceClient {
    
    @Autowired
    private ShortCodeServiceFacade shortCodeServiceFacade;
    
    public GenerateShortCodeLinkVO generateShortCodeLink(GenerateShortCodeLinkRequest req) {
        log.info("[[generateShortCodeLink]] 获取短链二维码链接  req:{}", JSON.toJSONString(req));
        try {
            Result<GenerateShortCodeLinkVO> result = shortCodeServiceFacade.generateShortCodeLink(req);
            if (null == result || !result.isSuccess()) {
                log.error("[[generateShortCodeLink]] 获取短链二维码链接 返回失败 req:{},resp:{}", JSON.toJSONString(req),JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("[[generateShortCodeLink]] 获取短链二维码链接 异常 req:{}", JSON.toJSONString(req),e);
        }
        return null;
    }
    
}
