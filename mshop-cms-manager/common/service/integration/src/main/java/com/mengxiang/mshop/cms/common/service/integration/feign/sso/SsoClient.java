package com.mengxiang.mshop.cms.common.service.integration.feign.sso;

import com.aikucun.security.sso.permission.dto.base.SsoPermissionResult;
import com.aikucun.security.sso.permission.dto.request.MenuQuery;
import com.aikucun.security.sso.permission.dto.request.RoleQuery;
import com.aikucun.security.sso.permission.dto.request.UserQuery;
import com.aikucun.security.sso.permission.dto.response.Menu;
import com.aikucun.security.sso.permission.dto.response.Role;
import com.aikucun.security.sso.permission.dto.response.UserInfo;
import com.aikucun.security.sso.permission.feign.UserPermissionClient;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.utils.datamasking.DataMask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * sso接口
 * <AUTHOR>
 */
@Component
@Slf4j
public class SsoClient {
    @Autowired
    private UserPermissionClient userPermissionClient;

    /**
     * 查询登录用户信息接口
     * @param req req
     * @return user info or null if not found
     */
    public UserInfo getUserInfo(UserQuery req) {
        try {
            SsoPermissionResult<UserInfo> res = userPermissionClient.getUserInfo(req);
            if (res != null && res.getSuccess()) {
                return res.getData();
            }
            log.warn("[调用 com.aikucun.security.sso.permission.feign.UserPermissionClient.getUserInfo 查询SSO登录用户信息接口异常，请求参数={},响应={}",
                    DataMask.toJSONString(req), DataMask.toJSONString(res));
        } catch (Exception e) {
            log.error("[调用 com.aikucun.security.sso.permission.feign.UserPermissionClient.getUserInfo 查询SSO登录用户信息接口异常，请求参数={}",
                    JSON.toJSONString(req), e);
        }
        return null;
    }

    /**
     * 查询用户的菜单信息
     * @param req req
     * @return user info or null if not found
     */
    public List<Menu> getUserMenus(MenuQuery req) {
        try {
            SsoPermissionResult<List<Menu>> res = userPermissionClient.getMenuList(req);
            if (res != null && res.getSuccess()) {
                return res.getData();
            }

            log.warn("[调用 com.aikucun.security.sso.permission.feign.UserPermissionClient.getUserMenus 查询SSO登录用户菜单接口异常，请求参数={},响应={}",
                    DataMask.toJSONString(req), DataMask.toJSONString(res));
        } catch (Exception e) {
            log.error("[调用 com.aikucun.security.sso.permission.feign.UserPermissionClient.getUserMenus 查询SSO登录用户菜单接口异常，请求参数={}",
                    JSON.toJSONString(req), e);
        }
        return new ArrayList<>();
    }

    /**
     * 查询用户的角色
     * @param req req
     * @return user info or null if not found
     */
    public List<Role> getUserRoles(RoleQuery req) {
        try {
            SsoPermissionResult<List<Role>> res = userPermissionClient.getRoleList(req);
            if (res != null && res.getSuccess()) {
                return res.getData();
            }

            log.warn("[调用 com.aikucun.security.sso.permission.feign.UserPermissionClient.getUserRoles 查询SSO登录用户角色接口异常，请求参数={},响应={}",
                    DataMask.toJSONString(req), DataMask.toJSONString(res));
        } catch (Exception e) {
            log.error("[调用 com.aikucun.security.sso.permission.feign.UserPermissionClient.getUserRoles 查询SSO登录用户角色接口异常，请求参数={}",
                    JSON.toJSONString(req), e);
        }
        return new ArrayList<>();
    }
}
