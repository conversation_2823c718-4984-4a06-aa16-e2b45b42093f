package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.alibaba.fastjson.JSONObject;
import com.x.base.common.util.Result;
import com.x.live.center.dto.LiveInfoDTO;
import com.x.live.center.remote.LiveFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Service
@Slf4j
public class AkcLiveClient {

    @Autowired
    private LiveFeignClient liveFeignClient;

    public LiveInfoDTO queryLiveBasics(String liveRoomId) {
        Result<LiveInfoDTO> result = liveFeignClient.queryLiveBasics(liveRoomId);
        if (Objects.isNull(result)) {
            log.warn("AkcLiveClient queryLiveBasics result is null, liveRoomId:{}", liveRoomId);
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("AkcLiveClient queryLiveBasics result is fail, liveRoomId:{},result:{}", liveRoomId, JSONObject.toJSONString(result));
            return null;
        }
        if (Objects.isNull(result.getData())) {
            log.warn("AkcLiveClient queryLiveBasics data is null, liveRoomId:{},result:{}", liveRoomId, JSONObject.toJSONString(result));
            return null;
        }
        return result.getData();
    }
}
