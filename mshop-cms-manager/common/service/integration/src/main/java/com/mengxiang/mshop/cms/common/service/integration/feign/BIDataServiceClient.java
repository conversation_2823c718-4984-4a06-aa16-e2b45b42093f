package com.mengxiang.mshop.cms.common.service.integration.feign;


import com.aikucun.common2.base.Result;
import com.aikucun.dc.aiward.facade.stub.rule.sell.SellRuleCreateDto;
import com.aikucun.dc.aiward.facade.stub.rule.sell.SellRuleFacade;
import com.aikucun.dc.aiward.facade.stub.selection.LabelsResponseDto;
import com.aikucun.dc.aiward.facade.stub.selection.SelectionFacade;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/8/26 19:15
 */
@Component
@Slf4j
public class BIDataServiceClient {
    @Value("${bi.apiFacade.logFlag:0}")
    private Integer apiFacadeLogFlag;
    @Resource
    private SellRuleFacade sellRuleFacade;
    @Resource
    private SelectionFacade selectionFacade;

    public Long createRuleByMerchantInfo(SellRuleCreateDto req){
        try {
            Result<Long> query = sellRuleFacade.createRuleByMerchantInfo(req);
            if (query != null && query.getSuccess() && Objects.nonNull(query.getData())) {
                if (1 == apiFacadeLogFlag) {
                    log.info("createRuleByMerchantInfo queryBIDataString req:{},resp:{}",
                            JSON.toJSONString(req),  JSON.toJSONString(query));
                }
                return query.getData();
            } else {
                log.error("createRuleByMerchantInfo queryBIDataString req:{},resp:{}",
                        JSON.toJSONString(req),  JSON.toJSONString(query));
            }
        } catch (Exception ex) {
            log.error("createRuleByMerchantInfo error req:{}", JSON.toJSONString(req), ex);
        }
        return null;
    }

    public LabelsResponseDto getLabelsByGroup(String labelGroup) {
        Result<LabelsResponseDto> result = selectionFacade.getLabelsByGroup(labelGroup);
        if (Objects.isNull(result)){
            log.error("selectionFacade.getLabelsByGroup result is null, req:{}", labelGroup);
            return null;
        }
        if (!result.getSuccess()) {
            log.error("selectionFacade.getLabelsByGroup result is fail, req:{},msg:{}", labelGroup, JSONObject.toJSONString(result));
            return null;
        }
        return result.getData();
    }
}
