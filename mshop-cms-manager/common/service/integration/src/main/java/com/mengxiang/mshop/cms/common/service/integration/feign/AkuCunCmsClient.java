package com.mengxiang.mshop.cms.common.service.integration.feign;

import cn.hutool.core.util.ObjectUtil;
import com.akucun.cms.aggregation.stub.feign.TenantResourceSwitchClient;
import com.akucun.cms.model.dto.business.TenantResourceSwitchDTO;
import com.akucun.cms.model.vo.business.CategorySwitchVO;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Objects;

/**
 * akucun-cms-manager外部接口调用
 */
@Slf4j
@Service
public class AkuCunCmsClient {

    @Autowired
    private TenantResourceSwitchClient tenantResourceSwitchClient;


    /**
     * 查询tab信息
     *
     * @param tenantResourceSwitchDTO
     * @return
     */
    public List<CategorySwitchVO> queryTenantResourceSwitchInfo(TenantResourceSwitchDTO tenantResourceSwitchDTO){
        Result<List<CategorySwitchVO>> ResultList = tenantResourceSwitchClient.queryTenantResourceSwitchInfo(tenantResourceSwitchDTO);
        if (Objects.isNull(ResultList)) {
            log.info("cmsAggregationClient queryTenantResourceSwitchInfo result is fail, result:{}", JSONObject.toJSONString(ResultList));
            return null;
        }
        if (!ResultList.isSuccess()) {
            log.info("cmsAggregationClient queryTenantResourceSwitchInfo result is fail, result:{}", JSONObject.toJSONString(ResultList));
            return null;
        }
        if (Objects.isNull(ResultList.getData())) {
            log.info("cmsAggregationClient queryTenantResourceSwitchInfo data is null, result:{}", JSONObject.toJSONString(ResultList));
            return null;
        }
        return ResultList.getData();
    }

    /**
     * 更新tab信息
     * @param dto
     */
    public void updateTenantResourceSwitch(@RequestBody TenantResourceSwitchDTO dto){
        Result result = tenantResourceSwitchClient.updateTenantResourceSwitch(dto);
        if(ObjectUtil.isNull(result) || !result.isSuccess()){
            log.warn("updateTenantResourceSwitch result is fail, result:{}", JSONObject.toJSONString(dto));
        }
    }
}
