package com.mengxiang.mshop.cms.common.service.integration.feign;


import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.saas.service.facade.common.feign.tenant.TenantFeign;
import com.mengxiang.saas.service.facade.common.feign.wechat.AppFeign;
import com.mengxiang.saas.service.facade.common.request.tenant.TenantComponentReq;
import com.mengxiang.saas.service.facade.common.response.wechat.AppAuthorizationInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 微信信息
 */
@Service
@Slf4j
public class TenantFeignClient {

    @Autowired
    private AppFeign appFeign;

    @Autowired
    private TenantFeign tenantFeign;

    public AppAuthorizationInfoResp queryAuthorizationInfo(Long tenantId) {
        Result<AppAuthorizationInfoResp> result = appFeign.queryAuthorizationInfo(tenantId);
        if (Objects.isNull(result)) {
            log.warn("TenantFeignClient queryAuthorizationInfo result is null, req:{}", JSON.toJSONString(tenantId));
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("TenantFeignClient queryAuthorizationInfo result is fail, req:{} resp:{}", JSON.toJSONString(tenantId), JSON.toJSONString(result));
            return null;
        }
        if (Objects.isNull(result.getData())) {
            log.warn("TenantFeignClient queryAuthorizationInfo data is null, req:{} resp:{}", JSON.toJSONString(tenantId), JSON.toJSONString(result));
            return null;
        }
        return result.getData();
    }

    public Object queryComponentValue(Long tenantId, String component) {
        try {
            Result<Object> rs = tenantFeign.queryComponentValue(tenantId, component);
            if (rs.isSuccess()) {
                return rs.getData();
            }
            log.error("获取租户component失败,tenantId={},component={},resp={}", tenantId, component, JSON.toJSONString(rs));
        } catch (Throwable e) {
            log.error("获取租户component异常,err={}", ExceptionUtils.getStackTrace(e));
        }
        return null;
    }

    public Boolean updateComponent(Long tenantId, List<TenantComponentReq> tenantComponentReqList) {
        try {
            Result<Boolean> rs = tenantFeign.updateComponent(tenantId, tenantComponentReqList);
            if (rs.isSuccess()) {
                return rs.getData();
            }
        } catch (Throwable e) {
            log.error("更新租户组件异常,err={}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }
}
