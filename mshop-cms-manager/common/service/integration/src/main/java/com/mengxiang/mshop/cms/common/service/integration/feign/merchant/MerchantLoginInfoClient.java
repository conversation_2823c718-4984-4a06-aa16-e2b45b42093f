package com.mengxiang.mshop.cms.common.service.integration.feign.merchant;

import com.mengxiang.merchant.platform.query.token.constant.BaseConstant;
import com.mengxiang.merchant.platform.query.token.entity.SysUserDTO;
import com.mengxiang.merchant.platform.query.token.service.MerUserTokenService;
import com.mengxiang.merchant.platform.query.token.util.MerRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 商户登录信息
 * <AUTHOR>
 */
@Component
@Slf4j
public class MerchantLoginInfoClient {

    @Value("${mer.tokenName:token}")
    private String tokenName;
    @Value("${mer.shopCodeName:shopCode}")
    private String shopCodeName;
    
    //@Autowired
    //private IMerUserTokenRemote iMerUserTokenRemote;
    
    @Autowired
    private MerUserTokenService merUserTokenService;
    
    //public SysUserDTO queryUserInfoByToken(String token) {
    //    try {
    //        Result<SysUserDTO> result = iMerUserTokenRemote.getLoginInfoByToken(token);
    //        if (null == result || !result.isSuccess()) {
    //            log.error("[[queryUserInfoByToken]] 查询用户信息失败 token:{}", token);
    //            return null;
    //        }
    //        SysUserDTO user = result.getData();
    //        return user;
    //    } catch (Exception e) {
    //        log.error("[[queryUserInfoByToken]] 查询用户信息 出现异常 token:{}", token, e);
    //    }
    //    return null;
    //}
    
    public SysUserDTO getLoginInfoByRequest(HttpServletRequest request) {
        try {
            SysUserDTO result = merUserTokenService.getLoginInfoByRequest(request);
            if (null == result) {
                return null;
            }
            return result;
        } catch (Exception e) {
            log.error("[[getLoginInfoByRequest]] 查询用户信息 出现异常", e);
        }
        return null;
    }
}
