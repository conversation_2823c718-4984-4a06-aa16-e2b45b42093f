package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.aggr.core.model.req.activity.ActivityListReq;
import com.mengxiang.mshop.aggr.core.model.req.base.AggrFeignBaseDTO;
import com.mengxiang.mshop.aggr.core.model.vo.activity.ActivityListVO;
import com.mengxiang.mshop.aggr.service.facade.common.feign.inner.ActivityInnerFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityClient {
    
    @Autowired
    private ActivityInnerFacade activityInnerFacade;
    
    public Pagination<ActivityListVO> activityList(AggrFeignBaseDTO<ActivityListReq> req) {

        try {
            Result<Pagination<ActivityListVO>> result = activityInnerFacade.activityList(req);
            if (null == result || !result.isSuccess()) {
                log.warn("ActivityClient activityList 活动列表 req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("ActivityClient activityList 活动列表 异常 req:{}", JSON.toJSONString(req));
        }
        return null;
    }
}
