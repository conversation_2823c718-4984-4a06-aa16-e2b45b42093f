package com.mengxiang.mshop.cms.common.service.integration.feign;

import com.akucun.activity.mgt.facade.stub.api.ScheduleApplyRpcService;
import com.akucun.activity.mgt.facade.stub.dto.request.scheduleapply.QueryScheduleProdsForMarketingRequest;
import com.akucun.activity.mgt.facade.stub.dto.response.scheduleapply.QueryScheduleProdsForMarketingRes;
import com.akucun.product.facade.stub.dto.req.revision.product.BaseProductRequest;
import com.akucun.product.facade.stub.dto.res.item.ItemPicDTO;
import com.akucun.product.facade.stub.dto.res.revision.product.SpuInfo;
import com.akucun.product.facade.stub.enums.ProductBaseQueryEnum;
import com.akucun.product.facade.stub.revision.product.IBaseProductFacade;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.mshop.aggr.core.model.req.base.AggrFeignBaseDTO;
import com.mengxiang.mshop.aggr.core.model.req.product.ProductListReq;
import com.mengxiang.mshop.aggr.core.model.vo.activity.ActivityListVO;
import com.mengxiang.mshop.aggr.core.model.vo.product.ProductListVO;
import com.mengxiang.mshop.aggr.core.model.vo.product.sub.SpuCategoryInfoVO;
import com.mengxiang.mshop.aggr.service.facade.common.feign.inner.ProductInnerFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ProductClient {

    @Autowired
    private ProductInnerFacade productInnerFacade;
    
    @Autowired
    private IBaseProductFacade iBaseProductFacade;

    @Autowired
    private ScheduleApplyRpcService scheduleApplyRpcService;

    public List<String> queryItemCodeList(List<Long> activitySpuCodes){
        QueryScheduleProdsForMarketingRequest request = new QueryScheduleProdsForMarketingRequest();
        request.setDeleteFlag("2");
        request.setActivitySpuCodes(activitySpuCodes);
        com.akucun.common.Result<List<QueryScheduleProdsForMarketingRes>> result = scheduleApplyRpcService.queryScheduleProdsForMarketing(request);
        if (result == null) {
            log.warn("scheduleApplyRpcService queryScheduleProdsForMarketing result is null, req:{}", JSON.toJSONString(activitySpuCodes));
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("scheduleApplyRpcService queryScheduleProdsForMarketing result is fail , req:{},resp:{}", JSON.toJSONString(activitySpuCodes), JSON.toJSONString(result));
            return null;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            log.warn("scheduleApplyRpcService queryScheduleProdsForMarketing data is empty , req:{},resp:{}", JSON.toJSONString(activitySpuCodes), JSON.toJSONString(result));
            return null;
        }
        return result.getData().stream().map(x -> x.getItemCode()).collect(Collectors.toList());
    }

    public List<QueryScheduleProdsForMarketingRes> queryScheduleProdsForMarketing(List<Long> activitySpuCodes){
        QueryScheduleProdsForMarketingRequest request = new QueryScheduleProdsForMarketingRequest();
        request.setDeleteFlag("2");
        request.setActivitySpuCodes(activitySpuCodes);
        com.akucun.common.Result<List<QueryScheduleProdsForMarketingRes>> result = scheduleApplyRpcService.queryScheduleProdsForMarketing(request);
        if (result == null) {
            log.warn("scheduleApplyRpcService queryScheduleProdsForMarketing result is null, req:{}", JSON.toJSONString(activitySpuCodes));
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("scheduleApplyRpcService queryScheduleProdsForMarketing result is fail , req:{},resp:{}", JSON.toJSONString(activitySpuCodes), JSON.toJSONString(result));
            return null;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            log.warn("scheduleApplyRpcService queryScheduleProdsForMarketing data is empty , req:{},resp:{}", JSON.toJSONString(activitySpuCodes), JSON.toJSONString(result));
            return null;
        }

        return result.getData();
    }

    public List<ProductListVO> batchQuerySpuInfo (List<Long> activitySpuCodes) {
        List<QueryScheduleProdsForMarketingRes> marketingRes = queryScheduleProdsForMarketing(activitySpuCodes);
        if (CollectionUtils.isEmpty(marketingRes)) {
            return null;
        }
        List<String> itemCodeList = marketingRes.stream().map(x -> x.getItemCode()).collect(Collectors.toList());

        BaseProductRequest request = new BaseProductRequest();
        request.setItemCodeList(itemCodeList);
        request.setNeedQueryDeleteItem(true);
        request.setChannel("mshop-cms-manager");
        List<ProductBaseQueryEnum> productBaseQueryEnumList = new ArrayList<>();
        productBaseQueryEnumList.add(ProductBaseQueryEnum.SPU_BASE);
        productBaseQueryEnumList.add(ProductBaseQueryEnum.SKU_BASE);
        productBaseQueryEnumList.add(ProductBaseQueryEnum.SPU_PROPERTY_PIC);
        productBaseQueryEnumList.add(ProductBaseQueryEnum.SPU_PRODUCT_DETAIL_PIC);
        request.setProductBaseQueryEnumList(productBaseQueryEnumList);
        com.aikucun.common2.base.Result<List<SpuInfo>> result = iBaseProductFacade.batchQuerySpuInfo(request);
        if (result == null) {
            log.warn("iBaseProductFacade batchQuerySpuInfo result is null, req:{}", JSON.toJSONString(itemCodeList));
            return null;
        }
        if (!result.getSuccess()) {
            log.warn("iBaseProductFacade batchQuerySpuInfo result is fail , req:{},resp:{}", JSON.toJSONString(itemCodeList), JSON.toJSONString(result));
            return null;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            log.warn("iBaseProductFacade batchQuerySpuInfo data is empty , req:{},resp:{}", JSON.toJSONString(itemCodeList), JSON.toJSONString(result));
            return null;
        }
        //spuID映射 key=itemCode value=activitySpuCode
        Map<String,QueryScheduleProdsForMarketingRes> spuCodeMap = marketingRes.stream().collect(Collectors.toMap(QueryScheduleProdsForMarketingRes::getItemCode, Function.identity(),(e1, e2)->e1));

        return converterProductListVO(result.getData(),spuCodeMap);
    }

    private List<ProductListVO> converterProductListVO(List<SpuInfo> spuInfoList,Map<String,QueryScheduleProdsForMarketingRes> spuCodeMap){
        List<ProductListVO> result = Lists.newArrayList();
        for(SpuInfo spuInfo:spuInfoList){
            ProductListVO productListVO = new ProductListVO();
            productListVO.setMerStyleNo(spuInfo.getMerStyleNo());
            if(CollectionUtils.isNotEmpty(spuInfo.getDetailImageList())){
                productListVO.setSkuMainImageUrls(spuInfo.getDetailImageList());
            }else{
                productListVO.setSkuMainImageUrls(spuInfo.getPropertyImageList().stream().map(ItemPicDTO::getFileUrl).collect(Collectors.toList()));
            }

            productListVO.setTitle(spuInfo.getTitle());
            QueryScheduleProdsForMarketingRes activitySpuCode = spuCodeMap.get(spuInfo.getItemCode());
            if(Objects.nonNull(activitySpuCode)){
                productListVO.setActivitySpuId(String.valueOf(activitySpuCode.getActivitySpuCode()));
                productListVO.setSpuOnline(Objects.equals(activitySpuCode.getUpDownStatus(), 1));
            }
            SpuCategoryInfoVO spuCategoryInfoVO = new SpuCategoryInfoVO();
            spuCategoryInfoVO.setFirstCategoryCode(spuInfo.getFirstCategoryCode());
            spuCategoryInfoVO.setFirstCategoryName(spuInfo.getFirstCategoryName());
            spuCategoryInfoVO.setSecondCategoryCode(spuInfo.getSecondCategoryCode());
            spuCategoryInfoVO.setSecondCategoryName(spuInfo.getSecondCategoryName());
            spuCategoryInfoVO.setCategoryCode(spuInfo.getCategoryCode());
            spuCategoryInfoVO.setCategoryName(spuInfo.getCategoryName());
            productListVO.setSpuCategoryInfoVO(spuCategoryInfoVO);

            result.add(productListVO);
        }
        return result;
    }

    public Pagination<ProductListVO> productList(AggrFeignBaseDTO<ProductListReq> req) {

        try {
            Result<Pagination<ProductListVO>> result = productInnerFacade.productList(req);
            if (null == result || !result.isSuccess()) {
                log.warn("ProductClient productList 商品列表 req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("ProductClient productList 商品列表 异常 req:{}", JSON.toJSONString(req));
        }
        return null;
    }


    public Pagination<ProductListVO> productListByRule(AggrFeignBaseDTO<ProductListReq> req) {

        try {
            Result<Pagination<ProductListVO>> result = productInnerFacade.productListByRule(req);
            if (null == result || !result.isSuccess()) {
                log.warn("ProductClient productListByRule 商品列表 req:{},resp:{}", JSON.toJSONString(req), JSON.toJSONString(result));
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("ProductClient productListByRule 商品列表 异常 req:{}", JSON.toJSONString(req));
        }
        return null;
    }
}
